--- NB the following sequences are missing from below. 

--cpap_visit_treatment_setting_cpap_visit_treatment_setting_i_seq
--equip_types_equipxsetting_id_seq
--list_healthservices_id_seq
--pas_pt_names_nameid_seq
--pred_gli_lv_lookup_id_seq
--prefs_client_fields_values_id_seq

-- UPDATE: Handled in next migration

BEGIN;

SELECT setval('ab_user_id_seq', (SELECT COALESCE(MAX(id), 1) FROM ab_user));
SELECT setval('activity_logs_id_seq', (SELECT COALESCE(MAX(id), 1) FROM activity_logs));
SELECT setval('alarm_control_rules_id_seq', (SELECT COALESCE(MAX(id), 1) FROM alarm_control_rules));
SELECT setval('alarms_id_seq', (SELECT COALESCE(MAX(id), 1) FROM alarms));
SELECT setval('audit_log_id_seq', (SELECT COALESCE(MAX(id), 1) FROM audit_log));
SELECT setval('control_methods_id_seq', (SELECT COALESCE(MAX(id), 1) FROM control_methods));
SELECT setval('control_methods_equipments_id_seq', (SELECT COALESCE(MAX(id), 1) FROM control_methods_equipments));
SELECT setval('control_methods_equipments_parameters_id_seq', (SELECT COALESCE(MAX(id), 1) FROM control_methods_equipments_parameters));
SELECT setval('control_methods_equipments_parameters_control_rules_id_seq', (SELECT COALESCE(MAX(id), 1) FROM control_methods_equipments_parameters_control_rules));
SELECT setval('control_rule_intervals_id_seq', (SELECT COALESCE(MAX(id), 1) FROM control_rule_intervals));
SELECT setval('control_rules_id_seq', (SELECT COALESCE(MAX(id), 1) FROM control_rules));
SELECT setval('cpap_visit_clinic_cpap_visit_clinic_id_seq', (SELECT COALESCE(MAX(cpap_visit_clinic_id), 1) FROM cpap_visit_clinic));
SELECT setval('data_integration_error_data_integration_error_id_seq', (SELECT COALESCE(MAX(data_integration_error_id), 1) FROM data_integration_error));

SELECT setval('datadump_definition_prov_dumpid_seq', (SELECT COALESCE(MAX(dumpid), 1) FROM datadump_definition_prov));
SELECT setval('datadump_definition_research_dumpid_seq', (SELECT COALESCE(MAX(dumpid), 1) FROM datadump_definition_research));
SELECT setval('datadump_definition_rft_dumpid_seq', (SELECT COALESCE(MAX(dumpid), 1) FROM datadump_definition_rft));
SELECT setval('datadump_definition_spt_dumpid_seq', (SELECT COALESCE(MAX(dumpid), 1) FROM datadump_definition_spt));
SELECT setval('datadump_definition_walk_dumpid_seq', (SELECT COALESCE(MAX(dumpid), 1) FROM datadump_definition_walk));

SELECT setval('doctors_id_seq', (SELECT COALESCE(MAX(id), 1) FROM doctors));
--doctor_id is text and not a sequence 
--SELECT setval('doctor_id_seq', (SELECT COALESCE(MAX(doctor_id), 1) FROM doctors));
SELECT setval('equip_allocation_equip_allocation_id_seq', (SELECT COALESCE(MAX(equip_allocation_id), 1) FROM equip_allocation));
SELECT setval('equip_register_device_id_seq', (SELECT COALESCE(MAX(device_id), 1) FROM equip_register));
SELECT setval('equip_register_locations_location_id_seq', (SELECT COALESCE(MAX(location_id), 1) FROM equip_register_locations));
SELECT setval('equip_setting_equip_setting_id_seq', (SELECT COALESCE(MAX(equip_setting_id), 1) FROM equip_setting));
SELECT setval('equip_types_equip_id_seq', (SELECT COALESCE(MAX(equip_id), 1) FROM equip_types));
SELECT setval('equip_types_equipxmode_id_seq', (SELECT COALESCE(MAX(id), 1) FROM equip_types_equipxmode));
SELECT setval('equip_types_modexsetting_id_seq', (SELECT COALESCE(MAX(id), 1) FROM equip_types_modexsetting));
SELECT setval('equipment_companies_id_seq', (SELECT COALESCE(MAX(id), 1) FROM equipment_companies));
SELECT setval('equipment_documents_id_seq', (SELECT COALESCE(MAX(id), 1) FROM equipment_documents));
SELECT setval('equipment_logs_id_seq', (SELECT COALESCE(MAX(id), 1) FROM equipment_logs));
SELECT setval('equipments_id_seq', (SELECT COALESCE(MAX(id), 1) FROM equipments));
SELECT setval('interface_log_interface_log_id_seq', (SELECT COALESCE(MAX(interface_log_id), 1) FROM interface_log));
SELECT setval('labs_id_seq', (SELECT COALESCE(MAX(id), 1) FROM labs));

SELECT setval('list_accesstypes_code_seq', (SELECT COALESCE(MAX(code), 1) FROM list_accesstypes));
SELECT setval('list_equip_categories_category_id_seq', (SELECT COALESCE(MAX(category_id), 1) FROM list_equip_categories));
SELECT setval('list_equip_distributors_distributor_id_seq', (SELECT COALESCE(MAX(distributor_id), 1) FROM list_equip_distributors));
SELECT setval('list_equip_manufacturers_manufacturer_id_seq', (SELECT COALESCE(MAX(manufacturer_id), 1) FROM list_equip_manufacturers));
SELECT setval('list_equip_models_model_id_seq', (SELECT COALESCE(MAX(model_id), 1) FROM list_equip_models));
SELECT setval('list_equip_modes_mode_id_seq', (SELECT COALESCE(MAX(mode_id), 1) FROM list_equip_modes));
SELECT setval('list_equip_settings_setting_id_seq', (SELECT COALESCE(MAX(setting_id), 1) FROM list_equip_settings));
SELECT setval('list_fit_exercises_fitexerciseid_seq', (SELECT COALESCE(MAX(fitexerciseid), 1) FROM list_fit_exercises));
SELECT setval('list_language_list_language_id_seq', (SELECT COALESCE(MAX(list_language_id), 1) FROM list_language));
SELECT setval('list_nationality_id_seq', (SELECT COALESCE(MAX(id), 1) FROM list_nationality));
SELECT setval('list_permissiontypes_permtypeid_seq', (SELECT COALESCE(MAX(permtypeid), 1) FROM list_permissiontypes));
SELECT setval('list_psg_types_id_seq', (SELECT COALESCE(MAX(id), 1) FROM list_psg_types));

-- SELECT setval('list_reportstatuses_statusid_seq', (SELECT COALESCE(MAX(statusid), 1) FROM list_reportstatuses));

SELECT setval('list_reportstatuses_sleep_statusid_seq', (SELECT COALESCE(MAX(statusid), 1) FROM list_reportstatuses_sleep));
SELECT setval('list_reportstyles_styleid_seq', (SELECT COALESCE(MAX(styleid), 1) FROM list_reportstyles));
SELECT setval('list_units_id_seq', (SELECT COALESCE(MAX(id), 1) FROM list_units));
SELECT setval('log_db_migration_migrationid_seq', (SELECT COALESCE(MAX(migrationid), 1) FROM log_db_migration));
SELECT setval('log_logins_id_seq', (SELECT COALESCE(MAX(id), 1) FROM log_logins));
SELECT setval('log_mergeurs_mergeid_seq', (SELECT COALESCE(MAX(mergeid), 1) FROM log_mergeurs));
SELECT setval('mask_allocation_mask_allocation_id_seq', (SELECT COALESCE(MAX(mask_allocation_id), 1) FROM mask_allocation));
SELECT setval('p_sleep_study_test_id_seq', (SELECT COALESCE(MAX(test_id), 1) FROM p_sleep_study));
SELECT setval('parameter_mapping_parametermappingid_seq', (SELECT COALESCE(MAX(parametermappingid), 1) FROM parameter_mapping));
SELECT setval('parameters_id_seq', (SELECT COALESCE(MAX(id), 1) FROM parameters));
SELECT setval('pas_pt_patientid_seq', (SELECT COALESCE(MAX(patientid), 1) FROM pas_pt));
SELECT setval('pas_pt_address_addressid_seq', (SELECT COALESCE(MAX(addressid), 1) FROM pas_pt_address));
SELECT setval('pas_pt_gp_gpid_seq', (SELECT COALESCE(MAX(gpid), 1) FROM pas_pt_gp));
SELECT setval('pas_pt_ur_numbers_ur_id_seq', (SELECT COALESCE(MAX(ur_id), 1) FROM pas_pt_ur_numbers));
SELECT setval('permission_id_seq', (SELECT COALESCE(MAX(id), 1) FROM permission));
SELECT setval('person_permissions_person_permissionid_seq', (SELECT COALESCE(MAX(person_permissionid), 1) FROM person_permissions));
SELECT setval('persons_personid_seq', (SELECT COALESCE(MAX(personid), 1) FROM persons));
SELECT setval('pred_equations_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_equations));
SELECT setval('pred_gli_tlco_lookup_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_gli_tlco_lookup));
SELECT setval('pred_lms_coeff_coefficientid_seq', (SELECT COALESCE(MAX(coefficientid), 1) FROM pred_lms_coeff));
SELECT setval('pred_lms_equations_gliid_seq', (SELECT COALESCE(MAX(gliid), 1) FROM pred_lms_equations));
SELECT setval('pred_noncaucasian_corrections_correctionid_seq', (SELECT COALESCE(MAX(correctionid), 1) FROM pred_noncaucasian_corrections));
SELECT setval('pred_ref_agegroups_agegroupid_seq', (SELECT COALESCE(MAX(agegroupid), 1) FROM pred_ref_agegroups));
SELECT setval('pred_ref_clipmethods_clipmethodid_seq', (SELECT COALESCE(MAX(clipmethodid), 1) FROM pred_ref_clipmethods));
SELECT setval('pred_ref_equationtypes_equationtypeid_seq', (SELECT COALESCE(MAX(equationtypeid), 1) FROM pred_ref_equationtypes));
SELECT setval('pred_ref_ethnicities_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_ref_ethnicities));
SELECT setval('pred_ref_ethnicity_correctionfactors_factorid_seq', (SELECT COALESCE(MAX(factorid), 1) FROM pred_ref_ethnicity_correctionfactors));
SELECT setval('pred_ref_ethnicity_correctionmethods_correctionmethodid_seq', (SELECT COALESCE(MAX(correctionmethodid), 1) FROM pred_ref_ethnicity_correctionmethods));
SELECT setval('pred_ref_genders_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_ref_genders));
SELECT setval('pred_ref_parameter_mapping_parameter_mapping_id_seq', (SELECT COALESCE(MAX(parameter_mapping_id), 1) FROM pred_ref_parameter_mapping));
SELECT setval('pred_ref_parameters_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_ref_parameters));
SELECT setval('pred_ref_sources_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_ref_sources));
SELECT setval('pred_ref_stattypes_stattypeid_seq', (SELECT COALESCE(MAX(stattypeid), 1) FROM pred_ref_stattypes));
SELECT setval('pred_ref_tests_testid_seq', (SELECT COALESCE(MAX(testid), 1) FROM pred_ref_tests));
SELECT setval('pred_ref_variables_variableid_seq', (SELECT COALESCE(MAX(variableid), 1) FROM pred_ref_variables));
SELECT setval('pred_sourcexethnicity_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_sourcexethnicity));
SELECT setval('pred_sourcexgender_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_sourcexgender));
SELECT setval('pred_sourcexparameter_sxpid_seq', (SELECT COALESCE(MAX(sxpid), 1) FROM pred_sourcexparameter));
SELECT setval('pred_sourcextest_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_sourcextest));
SELECT setval('prefs_app_strings_stringid_seq', (SELECT COALESCE(MAX(stringid), 1) FROM prefs_app_strings));
SELECT setval('prefs_bd_change_method_id_seq', (SELECT COALESCE(MAX(id), 1) FROM prefs_bd_change_method));
SELECT setval('prefs_client_fields_id_seq', (SELECT COALESCE(MAX(id), 1) FROM prefs_client_fields));
SELECT setval('prefs_client_fields_category_id_seq', (SELECT COALESCE(MAX(id), 1) FROM prefs_client_fields_category));
SELECT setval('prefs_fielditems_prefs_id_seq', (SELECT COALESCE(MAX(prefs_id), 1) FROM prefs_fielditems));
SELECT setval('prefs_fields_field_id_seq', (SELECT COALESCE(MAX(field_id), 1) FROM prefs_fields));
SELECT setval('prefs_pred_new_prefid_seq', (SELECT COALESCE(MAX(prefid), 1) FROM prefs_pred_new));
SELECT setval('prefs_report_parameters_reportid_seq', (SELECT COALESCE(MAX(reportid), 1) FROM prefs_report_parameters));
SELECT setval('prefs_reports_strings_stringid_seq', (SELECT COALESCE(MAX(stringid), 1) FROM prefs_reports_strings));
SELECT setval('prov_protocol_doseschedule_doseid_seq', (SELECT COALESCE(MAX(doseid), 1) FROM prov_protocol_doseschedule));
SELECT setval('prov_protocols_protocolid_seq', (SELECT COALESCE(MAX(protocolid), 1) FROM prov_protocols));
SELECT setval('prov_test_provid_seq', (SELECT COALESCE(MAX(provid), 1) FROM prov_test));
SELECT setval('prov_testdata_testdataid_seq', (SELECT COALESCE(MAX(testdataid), 1) FROM prov_testdata));
SELECT setval('r_attached_files_id_seq', (SELECT COALESCE(MAX(id), 1) FROM r_attached_files));
SELECT setval('r_cpet_cpetid_seq', (SELECT COALESCE(MAX(cpetid), 1) FROM r_cpet));
SELECT setval('r_cpet_import_field_maps_id_seq', (SELECT COALESCE(MAX(id), 1) FROM r_cpet_import_field_maps));
SELECT setval('r_cpet_import_files_id_seq', (SELECT COALESCE(MAX(id), 1) FROM r_cpet_import_files));
SELECT setval('r_cpet_levels_levelid_seq', (SELECT COALESCE(MAX(levelid), 1) FROM r_cpet_levels));
SELECT setval('r_hast_hastid_seq', (SELECT COALESCE(MAX(hastid), 1) FROM r_hast));
SELECT setval('r_hast_levels_levelid_seq', (SELECT COALESCE(MAX(levelid), 1) FROM r_hast_levels));
SELECT setval('r_hast_protocols_protocolid_seq', (SELECT COALESCE(MAX(protocolid), 1) FROM r_hast_protocols));
SELECT setval('r_psg_imported_files_id_seq', (SELECT COALESCE(MAX(id), 1) FROM r_psg_imported_files));
SELECT setval('r_sessions_sessionid_seq', (SELECT COALESCE(MAX(sessionid), 1) FROM r_sessions));
SELECT setval('r_spt_sptid_seq', (SELECT COALESCE(MAX(sptid), 1) FROM r_spt));
SELECT setval('r_spt_allergens_allergenid_seq', (SELECT COALESCE(MAX(allergenid), 1) FROM r_spt_allergens));
SELECT setval('r_walktests_protocols_protocolid_seq', (SELECT COALESCE(MAX(protocolid), 1) FROM r_walktests_protocols));
SELECT setval('r_walktests_trials_trialid_seq', (SELECT COALESCE(MAX(trialid), 1) FROM r_walktests_trials));
SELECT setval('r_walktests_trials_levels_levelid_seq', (SELECT COALESCE(MAX(levelid), 1) FROM r_walktests_trials_levels));
SELECT setval('r_walktests_v1heavy_walkid_seq', (SELECT COALESCE(MAX(walkid), 1) FROM r_walktests_v1heavy));
SELECT setval('report_pipeline_report_pipeline_id_seq', (SELECT COALESCE(MAX(report_pipeline_id), 1) FROM report_pipeline));
SELECT setval('reports_report_id_seq', (SELECT COALESCE(MAX(report_id), 1) FROM reports));
SELECT setval('resmed_resmed_id_seq', (SELECT COALESCE(MAX(resmed_id), 1) FROM resmed));
SELECT setval('result_mapping_result_mapping_id_seq', (SELECT COALESCE(MAX(result_mapping_id), 1) FROM result_mapping));
SELECT setval('rft_routine_rftid_seq', (SELECT COALESCE(MAX(rftid), 1) FROM rft_routine));
SELECT setval('rft_test_quality_id_seq', (SELECT COALESCE(MAX(id), 1) FROM rft_test_quality));
SELECT setval('role_id_seq', (SELECT COALESCE(MAX(id), 1) FROM role));
SELECT setval('role_permissions_id_seq', (SELECT COALESCE(MAX(id), 1) FROM role_permissions));
SELECT setval('session_data_id_seq', (SELECT COALESCE(MAX(id), 1) FROM session_data));
SELECT setval('sessions_id_seq', (SELECT COALESCE(MAX(id), 1) FROM sessions));
SELECT setval('sidebar_modules_id_seq', (SELECT COALESCE(MAX(id), 1) FROM sidebar_modules));
SELECT setval('site_id_seq', (SELECT COALESCE(MAX(id), 1) FROM site));
SELECT setval('site_config_healthservices_id_seq', (SELECT COALESCE(MAX(id), 1) FROM site_config_healthservices));
SELECT setval('site_config_reportstyles_id_seq', (SELECT COALESCE(MAX(id), 1) FROM site_config_reportstyles));
SELECT setval('site_config_testgroups_id_seq', (SELECT COALESCE(MAX(id), 1) FROM site_config_testgroups));
SELECT setval('site_settings_id_seq', (SELECT COALESCE(MAX(id), 1) FROM site_settings));
SELECT setval('spt_allergencategories_categoryid_seq', (SELECT COALESCE(MAX(categoryid), 1) FROM spt_allergencategories));
SELECT setval('spt_allergens_allergenid_seq', (SELECT COALESCE(MAX(allergenid), 1) FROM spt_allergens));
SELECT setval('spt_panelmembers_memberid_seq', (SELECT COALESCE(MAX(memberid), 1) FROM spt_panelmembers));
SELECT setval('spt_panels_panelid_seq', (SELECT COALESCE(MAX(panelid), 1) FROM spt_panels));
SELECT setval('user_permissions_id_seq', (SELECT COALESCE(MAX(id), 1) FROM user_permissions));
SELECT setval('user_site_control_id_seq', (SELECT COALESCE(MAX(id), 1) FROM user_site_control));
SELECT setval('user_sites_id_seq', (SELECT COALESCE(MAX(id), 1) FROM user_sites));
SELECT setval('xreport_queue_id_seq', (SELECT COALESCE(MAX(id), 1) FROM xreport_queue));
COMMIT;