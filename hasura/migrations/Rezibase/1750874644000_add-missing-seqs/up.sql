ALTER TABLE "public"."doctors" DROP COLUMN "doctor_id";

ALTER TABLE "public"."pred_equations" DROP CONSTRAINT IF EXISTS "fk_pred_equations_genderid";

ALTER TABLE "public"."pred_equations" ADD FOREIGN KEY ("genderid") REFERENCES "public"."pred_ref_genders" ("id") ON DELETE CASCADE;

DROP TABLE IF EXISTS "public"."address_book";

DROP TABLE IF EXISTS "public"."list_gender_notused";

BEGIN;

SELECT setval('cpap_visit_treatment_setting_cpap_visit_treatment_setting_i_seq', (SELECT COALESCE(MAX(cpap_visit_treatment_setting_id), 1) FROM cpap_visit_treatment_setting));
SELECT setval('equip_types_equipxsetting_id_seq', (SELECT COALESCE(MAX(id), 1) FROM equip_types_equipxsetting));
SELECT setval('list_healthservices_id_seq', (SELECT COALESCE(MAX(id), 1) FROM list_healthservices));
SELECT setval('pas_pt_names_nameid_seq', (SELECT COALESCE(MAX(nameid), 1) FROM pas_pt_names));
SELECT setval('pred_gli_lv_lookup_id_seq', (SELECT COALESCE(MAX(id), 1) FROM pred_gli_lv_lookup));
SELECT setval('prefs_client_fields_values_id_seq', (SELECT COALESCE(MAX(id), 1) FROM prefs_client_fields_values));

COMMIT;