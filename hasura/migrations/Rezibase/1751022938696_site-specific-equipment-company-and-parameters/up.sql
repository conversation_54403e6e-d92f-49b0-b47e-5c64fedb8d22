BEGIN;

ALTER TABLE "public"."equipment_companies"
    ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;

ALTER TABLE "public"."equipment_companies"
    ADD FOREIGN KEY ("site_id") REFERENCES "public"."site" ("id") ON DELETE CASCADE;

ALTER TABLE "public"."parameters"
    ADD COLUMN "site_id" int4 NOT NULL DEFAULT 1;

ALTER TABLE "public"."parameters"
    ADD FOREIGN KEY ("site_id") REFERENCES "public"."site" ("id") ON DELETE CASCADE;


-- Copy parameter for new sites as well

INSERT INTO parameters (site_id,
                        created,
                        updated,
                        abbreviation,
                        long_name,
                        unit,
                        decimal_places,
                        active,
                        inactivity_reason,
                        inactivity_date)
SELECT s.id,
       p.created,
       p.updated,
       p.abbreviation,
       p.long_name,
       p.unit,
       p.decimal_places,
       p.active,
       p.inactivity_reason,
       p.inactivity_date
FROM parameters p
         CROSS JOIN site s
WHERE p.site_id = 1
  AND s.id != 1;


-- Copy parameter for new sites as well
INSERT INTO equipment_companies (site_id,
                                 created,
                                 updated,

                                 name,
                                 address)
SELECT s.id,
       p.created,
       p.updated,
       p.name,
       p.address
FROM equipment_companies p
         CROSS JOIN site s
WHERE p.site_id = 1
  AND s.id != 1;

COMMIT;
