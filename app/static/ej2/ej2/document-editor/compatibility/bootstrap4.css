.e-documenteditor .e-close::before {
  content: '\e745';
  font-family: 'e-icons';
  font-size: 14px;
}

.e-documenteditor .e-de-op-search-icon::before {
  content: '\e724';
  font-family: 'e-icons';
  font-size: 12px;
}

.e-documenteditor .e-arrow-up::before {
  content: '\e78d';
  font-family: 'e-icons';
  font-size: 12px;
}

.e-documenteditor .e-arrow-down::before {
  content: '\e798';
  font-family: 'e-icons';
  font-size: 12px;
}

.e-documenteditor .e-de-op .e-de-op-close-icon {
  font-size: 12px;
  height: 20px;
  margin-left: -4px;
  margin-top: -3px;
  width: auto;
}

.e-documenteditor .e-de-op-close-icon::before {
  content: '\e745';
}

.e-documenteditor .e-de-op-search-close-icon::before {
  content: '\e745';
  font-family: 'e-icons';
  font-size: 10px;
}

.e-menu-item .e-de-cut::before {
  content: '\e73f';
}

.e-menu-item .e-de-spellcheck::before {
  content: '\e686';
}

.e-menu-item .e-de-copy::before {
  content: '\e77b';
}

.e-icon-btn .e-de-paste::before, .e-menu-item .e-de-paste::before {
  content: '\e739';
}

.e-menu-item .e-de-insertlink::before {
  content: '\e72e';
}

.e-menu-item .e-de-remove-hyperlink::before {
  content: '\e7a8';
}

.e-menu-item .e-de-fonts::before {
  content: '\e74b';
}

.e-menu-item .e-de-insertabove::before {
  content: '\e783';
}

.e-menu-item .e-de-insertbelow::before {
  content: '\e736';
}

.e-menu-item .e-de-insertleft::before {
  content: '\e737';
}

.e-menu-item .e-de-insertright::before {
  content: '\e70e';
}

.e-menu-item .e-de-deleterow::before {
  content: '\e738';
}

.e-menu-item .e-de-deletecolumn::before {
  content: '\e719';
}

.e-de-bold::before {
  content: '\e78b';
  font-family: 'e-icons';
}

.e-de-italic::before {
  content: '\e78e';
  font-family: 'e-icons';
}

.e-de-underline::before {
  content: '\e792';
  font-family: 'e-icons';
}

.e-de-indent::before {
  content: '\e702';
  font-family: 'e-icons';
}

.e-de-outdent::before {
  content: '\e722';
  font-family: 'e-icons';
}

.e-de-align-left::before {
  content: '\e76f';
  font-family: 'e-icons';
}

.e-de-align-center::before {
  content: '\e732';
  font-family: 'e-icons';
}

.e-de-align-right::before {
  content: '\e746';
  font-family: 'e-icons';
}

.e-menu-item .e-de-paragraph::before {
  content: '\e7a6';
}

.e-menu-item .e-de-table::before {
  content: '\e7ad';
}

.e-de-justify::before {
  content: '\e79b';
  font-family: 'e-icons';
}

.e-menu-item .e-de-delete-table::before {
  content: '\e7a2';
}

.e-menu-item .e-de-continue-numbering::before {
  content: '\e7b2';
}

.e-menu-item .e-de-restart-at::before {
  content: '\e7ae';
}

.e-menu-item .e-de-open-hyperlink::before {
  content: '\e7b5';
}

.e-menu-item .e-de-copy-hyperlink::before {
  content: '\e79a';
}

.e-menu-item .e-de-edit-hyperlink::before {
  content: '\e7af';
}

.e-de-icon-bullet-list-dot::before {
  content: '\e7a0';
  font-family: 'e-icons';
  font-size: 18px;
}

.e-de-icon-bullet-list-circle::before {
  content: '\e7b0';
  font-family: 'e-icons';
  font-size: 18px;
}

.e-de-icon-bullet-list-square::before {
  content: '\e7a5';
  font-family: 'e-icons';
  font-size: 18px;
}

.e-de-icon-bullet-list-tick::before {
  content: '\e7ab';
  font-family: 'e-icons';
  font-size: 18px;
}

.e-de-icon-bullet-list-flower::before {
  content: '\e7a4';
  font-family: 'e-icons';
  font-size: 18px;
}

.e-de-icon-bullet-list-arrow::before {
  content: '\e7b6';
  font-family: 'e-icons';
  font-size: 18px;
}

.e-de-icon-bullet-list-none::before {
  content: '\e7b3';
  font-family: 'e-icons';
  font-size: 18px;
}

.e-de-icon-autofit::before {
  content: '\e713';
  font-family: 'e-icons';
}

.e-de-icon-fixed-columnwidth::before {
  content: '\e740';
  font-family: 'e-icons';
}

.e-de-icon-auto-fitwindow::before {
  content: '\e747';
  font-family: 'e-icons';
}

.e-de-table-properties-alignment:hover {
  border-color: #5a8e8a;
}

.e-de-table-properties-alignment {
  border: 1px solid transparent;
}

.e-de-tablecell-alignment {
  border: 1px solid transparent;
}

.e-de-tablecell-alignment:hover {
  border-color: #5a8e8a;
}

.e-de-table-border-setting {
  border: 1px solid #f8f9fa;
  height: 48px;
  left: 5px;
  position: relative;
  top: 5px;
  width: 48px;
}

.e-de-table-border-setting-genral {
  border: 1px solid rgba(0, 0, 0, 0.26);
  display: inline-block;
  height: 60px;
  width: 60px;
}

.e-de-table-border-preview-genral {
  border: 1px solid rgba(0, 0, 0, 0.26);
  display: inline-block;
  height: 25px;
  width: 25px;
}

.e-de-table-border-inside-setting:hover {
  border: 1px solid #5a8e8a;
}

.e-de-table-border-preview {
  height: 24px;
  width: 24px;
}

.e-de-table-border-inside-preview:hover {
  border: 1px solid #5a8e8a;
}

.e-de-table-border-inside-setting-click {
  border: 1px solid #5a8e8a;
}

.e-de-table-border-inside-preview-click {
  border: 1px solid #5a8e8a;
}

.e-de-table-border-toptop-alignment::before {
  content: '\e7a3';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-table-border-topcenter-alignment::before {
  content: '\e7a9';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-table-border-topbottom-alignment::before {
  content: '\e7aa';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-table-border-bottomleft-alignment::before {
  content: '\e7a7';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-table-border-bottomcenter-alignment::before {
  content: '\e79c';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-table-border-bottomright-alignment::before {
  content: '\e79f';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-table-border-diagionalup-alignment::before {
  content: '\e7ca';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-table-border-diagionaldown-alignment::before {
  content: '\e7d8';
  font-size: 16px;
  left: 4px;
  position: absolute;
  top: 0;
}

.e-de-single-spacing::before {
  content: '\e7e7';
  font-family: 'e-icons';
}

.e-de-double-spacing::before {
  content: '\e7e9';
  font-family: 'e-icons';
}

.e-de-one-point-five-spacing::before {
  content: '\e7e5';
  font-family: 'e-icons';
}

.e-de-before-spacing::before {
  content: '\e7e4';
  font-family: 'e-icons';
}

.e-de-after-spacing::before {
  content: '\e7ea';
  font-family: 'e-icons';
}

.e-de-table-border-none-setting::before {
  content: '\e7f1';
  font-size: 46px;
  position: absolute;
}

.e-de-table-border-box-setting::before {
  content: '\e7f6';
  font-size: 42px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-all-setting::before {
  content: '\e7f3';
  font-size: 42px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-custom-setting::before {
  content: '\e7f2';
  font-size: 42px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-table-left-alignment::before {
  content: '\e7f5';
  font-size: 46px;
}

.e-de-table-center-alignment::before {
  content: '\e7ee';
  font-size: 46px;
}

.e-de-table-right-alignment::before {
  content: '\e7eb';
  font-size: 46px;
}

.e-de-tablecell-top-alignment::before {
  content: '\e7f7';
  font-size: 51px;
}

.e-de-tablecell-center-alignment::before {
  content: '\e7ed';
  font-size: 51px;
}

.e-de-tablecell-bottom-alignment::before {
  content: '\e7ec';
  font-size: 51px;
}

.e-item .e-de-paste-text::before {
  content: '\e685';
}

.e-item .e-de-paste-source::before {
  content: '\e684';
}

.e-item .e-de-paste-merge::before {
  content: '\e687';
}

.e-de-blink-cursor {
  border-left: 1px solid;
  pointer-events: none;
  position: absolute;
  z-index: 3;
}

.e-de-cursor-animation {
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-name: FadeInFadeOut;
}

@keyframes FadeInFadeOut {
  from {
    opacity: 1;
  }
  13% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  63% {
    opacity: 1;
  }
  to {
    opacity: 1;
  }
}

.e-de-text-target {
  border: 0;
  height: 1px;
  opacity: 0;
  outline-style: none;
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  top: -10000px;
  width: 625px;
}

.e-documenteditor .e-checkbox-wrapper .e-frame {
  height: 14px;
  line-height: 6px;
  width: 14px;
}

.e-documenteditor .e-checkbox-wrapper .e-label {
  font-size: 12px;
}

.e-documenteditor .e-de-op-close-button {
  height: 20px;
  left: 267px;
  position: absolute;
  top: 18px;
  width: 20px;
}

.e-documenteditor .e-de-op-close-button.e-de-rtl {
  left: 14px;
}

.e-de-background {
  background-color: #e9ecef;
}

.e-de-result-list-block .e-de-search-result-hglt {
  background: transparent;
  border-bottom: 2px solid #5a8e8a;
  cursor: default;
  padding: 15px 1px 15px 5px;
}

.e-de-result-list-block .e-de-op-search-txt .e-de-op-search-word-text {
  color: #5a8e8a;
}

.e-de-search-result-item {
  cursor: default;
  padding: 15px 1px 15px 5px;
  word-break: break-word;
}

.e-de-search-result-item:hover {
  border-bottom: 1px solid #5a8e8a;
  cursor: default;
}

.e-de-search-result-item:focus {
  border-bottom: 2px solid #5a8e8a;
  cursor: default;
  padding: 15px 1px 15px 5px;
}

.e-de-search-tab-content .e-input-group .e-de-op-search-icon:focus {
  border: 1px solid #ddd;
}

.e-de-op-search-icon:hover {
  background: #ddd;
}

.e-de-search-tab-content .e-input-group .e-de-op-search-close-icon:focus {
  border: 1px solid #ddd;
  border-right-width: none;
}

.e-de-op-search-close-icon:hover {
  background: #ddd;
}

.e-spin-down:focus {
  border: 1px solid #ddd;
  border-right-width: none;
}

.e-spin-down:hover {
  background: #ddd;
}

.e-spin-up:focus {
  border: 1px solid #ddd;
  border-right-width: none;
}

.e-spin-up:hover {
  background: #ddd;
}

.e-de-para-dlg-heading {
  color: #212529;
  display: block;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 14px;
}

.e-de-para-dlg-container {
  height: auto;
  width: auto;
}

.e-de-para-dlg-cs-check-box {
  margin-bottom: 8px;
  margin-top: 8px;
}

.e-de-para-dlg-spacing-div {
  margin-left: 40px;
}

.e-de-para-dlg-spacing-div.e-de-rtl {
  margin-left: 0;
  margin-right: 40px;
}

.e-de-toc-dlg-heading {
  color: #212529;
  display: block;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 5px;
}

.e-de-toc-dlg-main-heading {
  color: #212529;
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
}

.e-content-placeholder.e-documenteditor.e-placeholder-documenteditor {
  background-size: 100%;
}

.e-de-toc-reset-button {
  margin-top: 5px;
}

.e-de-toc-reset-button.e-de-rtl {
  margin-right: 6px;
}

.e-de-toc-modify-button {
  margin-left: 121px;
  margin-top: 10px;
}

.e-de-toc-modify-button.e-de-rtl {
  margin-left: 0;
  margin-right: 138px;
}

.e-de-toc-dlg-container {
  height: 454px;
  width: 488px;
}

.e-de-toc-dlg-sub-container {
  margin-bottom: 15px;
}

.e-de-toc-list-view {
  border: 1px solid #c3c3c3;
  border-radius: 3px;
  font-size: 12px;
  height: 161px;
  margin-left: -21px;
  overflow-y: scroll;
  width: 209px;
}

.e-de-toc-list-view.e-de-rtl {
  margin-left: 0;
  margin-right: -21px;
}

.e-de-toc-dlg-sub-heading {
  color: #212529;
  display: block;
  font-size: 14px;
  font-weight: 400;
  margin: 5px 15px 5px 15px;
}

.e-de-toc-dlg-style-label {
  position: absolute;
  top: 54px;
}

.e-de-pagesetup-dlg-container {
  height: auto;
  width: 354px;
}

.e-de-page-setup-ppty-tab {
  border: 0;
}

.e-de-page-setup-dlg-sub-container {
  margin-bottom: 30px;
}

.e-de-page-setup-dlg-left-sub-container {
  float: left;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-left-sub-container.e-de-rtl {
  float: right;
}

.e-de-page-setup-dlg-right-sub-container {
  float: right;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-right-sub-container.e-de-rtl {
  float: left;
}

.e-de-page-setup-dlg-sub-header {
  display: block;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 8px;
  margin-top: 14px;
}

.e-de-page-setup-dlg-sub-title-header {
  display: block;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 8px;
  margin-top: 14px;
}

.e-de-page-setup-dlg-sub-container-port {
  height: auto;
  margin-bottom: 0;
}

.e-de-page-setup-dlg-sub-label {
  font-size: 13px;
  font-weight: 450;
}

.e-de-page-setup-dlg-orientation-prop {
  margin-top: 13px;
}

.e-de-page-setup-dlg-sub-size-container {
  height: 73px;
  margin-bottom: 20px;
}

.e-de-page-setup-dlg-layout-sub-container {
  height: auto;
  margin-bottom: 20px;
  position: relative;
  top: 14px;
}

.e-de-page-setup-dlg-first-page-prop .e-label, .e-de-page-setup-dlg-odd-or-even-prop .e-label {
  font-size: 12px;
}

.e-de-page-setup-dlg-first-page-prop .e-frame, .e-de-page-setup-dlg-odd-or-even-prop .e-frame {
  height: 15px;
  line-height: 13px;
  width: 15px;
}

.e-de-page-setup-dlg-left-layout-container {
  float: left;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-left-layout-container.e-de-rtl {
  float: right;
}

.e-de-page-setup-dlg-right-layout-container {
  float: right;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-right-layout-container.e-de-rtl {
  float: left;
}

.e-de-dlg-sub-header {
  display: block;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 8px;
  margin-top: 0;
}

.e-de-para-dlg-sub-container .e-input-group {
  margin-bottom: 8px;
}

.e-de-para-dlg-sub-container {
  margin-bottom: 14px;
}

.e-de-para-dlg-right-sub-container {
  top: 33px;
}

.e-de-dlg-footer .e-btn {
  margin-left: 10px;
}

.e-de-hyperlink-dlg-title {
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 8px;
  margin-top: 8px;
}

.e-de-hyperlink .e-de-hyperlink-dlg-input {
  height: 32px;
  margin-bottom: 8px;
  width: 230px;
}

.e-de-font-dlg-header {
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 8px;
}

.e-de-font-dlg-header-effects, .e-de-font-dlg-header-font-color {
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  font-weight: normal;
  margin-bottom: 8px;
}

.e-de-font-dlg-main-header {
  color: #212529;
  font-size: 14px;
  font-weight: normal;
  margin-right: 17px;
}

.e-de-font-dlg-cb-right {
  margin-left: 40px;
}

.e-de-font-dlg-cb-right.e-de-rtl {
  margin-left: 0;
  margin-right: 40px;
}

.e-de-font-dlg-cb-right-div {
  margin-left: 20px;
}

.e-de-dropdown {
  margin-right: 20px;
}

.e-de-restrict-pane, .e-de-op {
  border-right: 1px solid #dee2e6;
  padding-left: 14px;
  padding-top: 14px;
  position: relative;
  width: 300px;
}

.e-de-op.e-de-rtl {
  padding-left: 0;
  padding-right: 14px;
}

.e-de-op-header {
  color: #212529;
  font-family: "Helvetica";
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 0;
}

.e-de-op-header.e-de-rtl {
  direction: rtl;
}

.e-de-op-tab {
  border: 0;
  height: 40px;
}

.e-de-op-icon {
  color: rgba(0, 0, 0, 0.54);
  height: 20px;
  width: 20px;
}

.e-de-op-close-icon {
  color: #212529;
}

.e-de-op-nav-btn {
  height: 20px;
  width: 20px;
}

.e-de-op-search-txt {
  border-bottom: 1px solid #ddd;
  color: #212529;
  font-size: 14px;
}

.e-de-op-search-txt .e-de-op-search-word {
  color: #5a8e8a;
}

.e-de-op-more-less {
  display: block;
  margin-top: 14px;
}

.e-de-op-replacetabcontentdiv {
  height: 82px;
  margin-top: 14px;
}

label[for*='_wholeWord_e-de-ltr'] {
  left: 35px;
}

label[for*='_wholeWord_e-de-rtl'] {
  right: 35px;
}

.e-de-cell-dia-label-common {
  display: inline-block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 8px;
  margin-top: 0;
  width: 150px;
}

.e-de-cell-dia-options-label {
  font-size: 14px;
  font-weight: 400;
}

.e-de-table-border-heading {
  font-size: 16px;
  font-weight: 400;
  padding-bottom: 20px;
}

.e-de-table-setting-heading {
  font-size: 14px;
  font-weight: normal;
  padding-bottom: 20px;
}

.e-de-layout-setting-heading {
  font-size: 14px;
  font-weight: normal;
  padding-bottom: 20px;
}

.e-de-table-setting-labels-heading {
  font-size: 12px;
  font-weight: normal;
}

.e-de-table-element-subheading {
  font-size: 12px;
  font-weight: normal;
}

.e-de-border-dlg-preview-div {
  border: 1px solid rgba(0, 0, 0, 0.54);
}

.e-de-border-dlg-preview-inside-divs {
  opacity: 0.54;
}

.e-de-table-dia-align-div {
  border: 1px solid #ddd;
  display: inline-block;
  height: 60px;
  margin-right: 10px;
  width: 60px;
}

.e-de-table-dia-align-div.e-de-rtl {
  margin-left: 10px;
  margin-right: 0;
}

.e-de-table-dia-align-label {
  display: inline-block;
  font-size: 12px;
  font-weight: normal;
  margin-left: 10px;
  margin-top: 10px;
}

.e-de-table-dialog-separator-line {
  background-color: #dee2e6;
  bottom: 59px;
  display: none;
  height: 1px;
  left: 1px;
  margin-top: 5px;
  position: absolute;
  width: 100%;
}

.e-de-table-alignment-active {
  border: 1px solid #5a8e8a;
}

.e-de-table-dialog-options-label {
  font-size: 14px;
  font-weight: 400;
  padding-bottom: 8px;
  padding-top: 14px;
}

.e-de-list-ddl-header {
  font-size: 14px;
  font-weight: normal;
  margin-bottom: 15px;
  margin-top: 15px;
}

.e-de-list-ddl-header-list-level {
  font-size: 14px;
  font-weight: normal;
  margin-bottom: 15px;
}

.e-de-tbl-dlg-footer {
  padding-top: 23px;
}

.e-de-row-ht-top {
  padding: 0 20px;
}

.e-de-ht-wdth-type {
  margin-top: -24px;
}

.e-de-row-ht-top.e-de-rtl {
  margin-left: 0;
  margin-right: 20px;
}

.e-de-cell-width-top {
  margin-left: 20px;
  margin-top: -23px;
}

.e-de-cell-width-top.e-de-rtl {
  margin-left: 0;
  margin-right: 20px;
}

.e-de-tbl-dlg-border-btn {
  float: right;
  margin-top: 14px;
}

.e-de-tbl-dlg-border-btn.e-de-rtl {
  margin-right: 0;
}

.e-de-table-border-setting.e-de-rtl {
  right: 5px;
}

.e-de-tbl-dlg-op-btn {
  left: 440px;
  position: absolute;
  top: 285px;
}

.e-de-insert-table-dlg-sub-header {
  display: block;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 8px;
  margin-top: 0;
}

.e-de-insert-table-dlg-input {
  margin-bottom: 14px;
}

.e-de-list-ddl-subheader, .e-de-list-ddl-subheaderbottom {
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 10px;
  margin-top: 10px;
}

.e-de-list-dlg-subdiv {
  float: right;
  margin-top: 40px;
  position: relative;
}

.e-de-list-dlg-subdiv.e-de-rtl {
  float: left;
  margin-top: -136px;
}

.e-de-list-dlg-div {
  float: right;
  margin-top: 30px;
  position: relative;
}

.e-de-list-dlg-div.e-de-rtl {
  float: left;
  margin-top: -138px;
}

.e-de-ok-button {
  margin-right: 8px;
}

.e-de-ok-button.e-de-rtl {
  margin-left: 8px;
}

.e-de-options-setter {
  left: 339px;
}

.e-de-op-close-icon:hover {
  color: rgba(0, 0, 0, 0.75);
}

.e-de-tooltip {
  background-color: #fff;
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  cursor: text;
  max-width: 200px;
  padding: 5px;
  width: -webkit-fit-content;
  width: fit-content;
  word-wrap: break-word;
}

.e-btn.e-de-op-icon-btn {
  background-color: transparent;
  border-color: transparent;
}

.e-documenteditor .e-de-op-close-button {
  height: 20px;
  left: 267px;
  position: absolute;
  top: 18px;
  width: 20px;
}

.e-de-style-font-color-picker .e-split-btn-wrapper .e-split-colorpicker.e-split-btn, .e-de-style-font-color-picker .e-btn.e-icon-btn, .e-de-dlg-clr-picker .e-split-btn-wrapper .e-split-colorpicker.e-split-btn, .e-de-dlg-clr-picker .e-btn.e-icon-btn, .e-de-font-dlg-display .e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn, .e-de-font-dlg-display .e-colorpicker-wrapper .e-btn.e-icon-btn {
  padding: 4px 8px !important;
}

.e-de-style-font-group-button .e-btn, .e-de-style-paragraph-group-button .e-btn, .e-de-style-paragraph-indent-group-button .e-btn {
  background-color: #fff;
  border-color: #ced4da;
}

.e-de-style-font-group-button .e-btn:focus, .e-de-style-paragraph-group-button .e-btn:focus, .e-de-style-paragraph-indent-group-button .e-btn:focus {
  background-color: #6c757d;
  border-color: inset 0 3px 5px #4e555b;
}

.e-de-style-font-group-button .e-btn:active, .e-de-style-paragraph-group-button .e-btn:active, .e-de-style-paragraph-indent-group-button .e-btn:active {
  background-color: #6c757d;
  border-color: inset 0 3px 5px #4e555b;
}

.e-de-style-font-group-button .e-btn:hover, .e-de-style-paragraph-group-button .e-btn:hover, .e-de-style-paragraph-indent-group-button .e-btn:hover {
  background-color: #6c757d;
  border-color: inset 0 3px 5px #4e555b;
}

.e-de-style-font-group-button .e-btn:hover .e-btn-icon, .e-de-style-font-group-button .e-btn:focus .e-btn-icon, .e-de-style-font-group-button .e-btn:active .e-btn-icon, .e-de-style-font-group-button .e-btn:disabled .e-btn-icon, .e-de-style-paragraph-group-button .e-btn:hover .e-btn-icon, .e-de-style-paragraph-group-button .e-btn:focus .e-btn-icon, .e-de-style-paragraph-group-button .e-btn:active .e-btn-icon, .e-de-style-paragraph-group-button .e-btn:disabled .e-btn-icon {
  color: #fff;
}

.e-de-style-font-group-button .e-btn-icon, .e-de-style-paragraph-group-button .e-btn-icon, .e-de-style-paragraph-indent-group-button .e-btn-icon {
  color: #495057;
  font-size: 14px;
}

.e-de-style-paragraph-indent-group-button .e-btn:hover .e-btn-icon, .e-de-style-paragraph-indent-group-button .e-btn:focus .e-btn-icon, .e-de-style-paragraph-indent-group-button .e-btn:active .e-btn-icon, .e-de-style-paragraph-indent-group-button .e-btn:disabled .e-btn-icon {
  color: #fff;
}

.e-de-style-paragraph-indent-group-button .e-btn-icon {
  color: #495057;
  font-size: 14px;
}

.e-de-list-container {
  background: #fff !important;
}

.e-de-style-paragraph-indent-group-button .e-btn.e-active, .e-de-style-paragraph-group-button .e-btn.e-active, .e-de-style-font-group-button .e-btn.e-active {
  background-color: #6c757d;
  border-color: inset 0 3px 5px #4e555b;
  box-shadow: none;
}

.e-de-style-paragraph-indent-group-button .e-btn.e-active .e-btn-icon, .e-de-style-paragraph-group-button .e-btn.e-active .e-btn-icon, .e-de-style-font-group-button .e-btn.e-active .e-btn-icon {
  color: #fff;
}

.e-documenteditor .e-de-op-close-button {
  top: 10px;
}

.e-de-styles, .e-de-bookmark {
  margin-top: 0 !important;
}

.e-styles-common {
  padding-top: 14px;
}

.e-styles-list {
  margin-right: 14px;
}

.e-bigger .e-de-style-left-div .e-de-style-dlg-name-input {
  height: 41px;
}

.e-bigger .e-de-style-bold-button-size {
  height: 41px;
}

.e-bigger .e-styles-common {
  padding-top: 16px;
}

.e-bigger .e-styles-list {
  margin-right: 16px;
}

.e-bigger .e-bookmark-gotobutton, .e-bigger .e-bookmark-addbutton, .e-bigger .e-styles-addbutton, .e-bigger .e-bookmark-deletebutton {
  margin-bottom: 16px;
}

.e-bigger .e-de-search-tab-content {
  margin-top: 14px;
  width: 275px;
}

.e-bigger .e-de-op-more-less {
  display: block;
  margin-top: 16px;
}

.e-bigger .e-de-op-dlg-footer {
  margin-top: 16px;
}

.e-split-btn-wrapper .e-btn {
  border-color: #ced4da;
}

.e-documenteditor .e-de-op-close-button.e-de-rtl {
  right: 267px;
}

.e-btn.e-de-op-close-button:hover {
  background-color: transparent;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.75);
}

.e-btn.e-de-op-close-button:focus {
  background-color: transparent;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.75);
}

.e-btn.e-de-op-close-button:active {
  background-color: transparent;
  border-color: transparent;
  color: rgba(248, 249, 250, 0.5);
}

.e-documenteditor .e-input {
  color: #000;
  font-size: 14px;
}

.e-de-dlg-target .e-footer-content .e-control.e-btn.e-flat:not(.e-icon-btn) {
  height: 32px;
}

.e-de-tbl-dlg-border-btn .e-control.e-btn.e-flat:not(.e-icon-btn) {
  height: auto;
}

.e-de-op-result-container {
  margin-top: 14px;
}

.e-de-restrict-pane, .e-de-op {
  background-color: #f8f9fa;
}

.e-de-restrict-pane .e-tab-header .e-toolbar-items, .e-de-op .e-tab-header .e-toolbar-items {
  margin-bottom: 0;
  margin-top: 14px;
}

.e-de-font-dlg-color {
  border: 1px #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  height: 16px;
  margin-left: 15px;
  width: 25px;
}

.e-de-icon-table-row-above {
  top: 10px;
}

.e-de-icon-table-row-below {
  top: 49px;
}

.e-de-icon-table-column-left {
  top: 89px;
}

.e-de-icon-table-column-right {
  top: 127px;
}

.e-de-icon-table-delete {
  top: 10px;
}

.e-de-icon-table-row-delete {
  top: 49px;
}

.e-de-icon-table-column-delete {
  top: 89px;
}

.e-de-list-bullet-none {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-dot {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-circle {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-square {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-flower {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-arrow {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-tick {
  height: 40px;
  width: 40px;
}

.e-de-bullet:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-list-numbered-none {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-number-dot {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-number-brace {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-up-roman {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-up-letter {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-low-letter-brace {
  height: 80px;
  width: 80px;
}

.e-de-numbered-low-letter-dot {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-low-roman {
  height: 80px;
  width: 80px;
}

.e-de-numbered:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-list-multilevel-none {
  height: 80px;
  width: 80px;
}

.e-de-list-multilevel-list-normal {
  height: 80px;
  width: 80px;
}

.e-de-list-multilevel-list-multilevel {
  height: 80px;
  width: 80px;
}

.e-de-list-multilevel-list-bullets {
  height: 80px;
  width: 80px;
}

.e-de-multilevel-list:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-list-dialog-open:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-cell-options {
  left: 336px;
  top: 272px;
}

.e-de-cell-options.e-de-rtl {
  left: 123px;
}

.e-de-font-color-label {
  margin-bottom: 0;
  margin-top: 14px;
}

.e-de-font-content-label {
  width: 115px;
}

.e-de-font-color-margin {
  margin-right: 8px;
  margin-top: 5px;
}

.e-de-font-color-margin.e-de-rtl {
  margin-left: 8px;
  margin-right: 0;
}

.e-de-font-content-checkbox-label {
  margin-left: 42px;
}

.e-de-font-content-checkbox-label-rtl {
  margin-right: 61px;
}

.e-bigger .e-de-font-content-checkbox-label {
  margin-left: 64px;
}

.e-bigger .e-de-font-content-checkbox-label-rtl {
  margin-right: 64px;
}

.e-bigger .e-de-font-content-label {
  width: 132px;
}

.e-de-font-checkbox {
  margin-left: 58px;
}

.e-de-font-checkbox.e-de-rtl {
  margin-left: 0;
  margin-right: 58px;
}

.e-de-font-checkbox.e-de-rtl {
  margin-left: 0;
  margin-right: 58px;
}

.e-de-font-dlg-padding {
  margin-top: 14px;
}

.e-de-table-container-div {
  margin-top: 12px;
}

.e-de-table-header-div {
  padding-top: 0;
}

.e-de-table-subheader-div {
  float: right;
  margin-right: 140px;
  margin-top: -40px;
}

.e-de-table-subheader-div.e-de-rtl {
  float: left;
  margin-left: 140px;
  margin-right: 0;
}

.e-de-table-cell-header-div {
  padding-top: 0;
}

.e-de-table-cell-subheader-div {
  top: 57px;
}

.e-de-cell-margin-header {
  left: -26px;
  top: 274px;
}

.e-de-font-dlg-display {
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.e-de-tbl-margin-sub-header {
  margin-top: 10px;
}

.e-de-tbl-btn-seperator {
  width: 60%;
}

.e-de-op-msg {
  color: #000;
  top: 79px;
}

.e-de-save-dlg-file-name {
  height: 25px;
  margin-bottom: 8px;
}

.e-de-save-dlg-format-type {
  height: 25px;
  margin-bottom: 8px;
  padding-top: 1px;
}

.e-de-search-tab-content {
  margin-right: 14px;
  margin-top: 14px;
  width: 275px;
}

.e-de-font-dlg {
  width: 380px;
}

.e-de-hyperlink {
  height: auto;
  width: auto;
}

.e-de-insert-table {
  height: auto;
  width: auto;
}

.e-de-insert-spellchecker {
  height: 360px;
  width: 513px;
}

.e-de-dlg-spellcheck-listview {
  border: 1px solid #e4e4e4;
  border-radius: 2px !important;
  height: 122px !important;
  margin-top: 8px;
  position: relative;
  float: left;
  width: 343px;
}

.e-de-spellcheck-error-container {
  height: 140px;
  margin-bottom: 25px;
}

.e-de-spellcheck-suggestion-container {
  height: 140px;
}

.e-dlg-spellcheck-listitem {
  font-size: 15px !important;
}

.e-de-spellcheck-btncontainer {
  margin-top: 8px;
  position: relative;
  width: 154px;
  float: right;
}

.e-de-spellcheck-btn {
  height: 36px;
  margin-bottom: 8px;
  width: 154px;
}

.e-de-dlg-spellchecker-subheader {
  font-size: 15px;
  margin-top: 8px;
}

.e-de-dlg-spellchecker-subheaderbtm {
  font-size: 15px;
}

.e-de-list-dlg {
  height: 450px;
  width: 400px;
}

.e-de-save-dlg {
  height: 135px;
  width: 230px;
}

.e-de-table-properties-dlg {
  width: 410px;
}

.e-de-table-border-shading-dlg {
  height: 438px;
  width: 460px;
}

.e-de-table-cell-margin-dlg {
  height: auto;
  width: 380px;
}

.e-de-table-options-dlg {
  height: auto;
  width: 380px;
}

.e-de-table-border-none {
  position: absolute;
  top: 25px;
}

.e-de-table-border-box {
  position: absolute;
  top: 95px;
}

.e-de-table-border-all {
  position: absolute;
  top: 165px;
}

.e-de-table-border-custom {
  position: absolute;
  top: 235px;
}

.e-de-table-shading-preview {
  top: 385px;
}

.e-de-font-content-label span.e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-font-label span.e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-font-content-label:hover .e-label, .e-css.e-de-font-content-label:hover .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-font-label:hover .e-label, .e-css.e-de-font-label:hover .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-documenteditor .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
  font-size: 14px;
}

.e-documenteditor .e-checkbox-wrapper .e-frame {
  height: 15px;
  line-height: 13px;
  width: 15px;
}

.e-de-op-dlg-footer {
  margin-top: 14px;
}

.e-de-op-dlg-footer .e-btn {
  padding-left: 6px;
  padding-right: 6px;
}

.e-de-search-tab-content .e-input-group .e-de-search-input {
  width: 204px;
}

.e-de-op-replacewith {
  width: 98%;
}

.e-de-table-ppty-tab {
  border: 0;
}

.e-de-table-container-div .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-table-header-div .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-table-ppty-options-break .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-table-ppty-options-header-row .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-table-cell-header-div .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-tbl-margin-sub-header .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-tbl-btn-seperator .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
}

.e-de-list-format-info {
  border-radius: 50%;
  cursor: default;
  font-size: 12px;
  height: 15px;
  line-height: 1px;
  padding: 3px 0 0 0;
  text-transform: lowercase;
  width: 16px;
}

.e-button-custom {
  height: 32px;
  padding: 0;
  width: 70px;
}

.e-bigger .e-button-custom {
  height: 37px;
  width: 88px;
}

.e-styles-listview, .e-bookmark-listview {
  border: 1px solid #c8c8c8;
  border-radius: 4px;
  height: 150px;
  overflow-y: scroll;
}

.e-bookmark-gotobutton, .e-bookmark-addbutton, .e-styles-addbutton, .e-bookmark-deletebutton {
  margin-bottom: 8px;
}

.e-bookmark-list {
  float: left;
  margin-right: 14px;
  width: 250px;
}

.e-bookmark-list.e-de-rtl {
  margin-left: 20px;
  margin-right: 0;
}

.e-bookmark-textboxdiv {
  margin-bottom: 8px;
}

.e-bookmark-listview .e-list-item {
  font-size: 13px;
  height: 30px;
  line-height: 27px;
}

.e-bookmark-common {
  display: -ms-flexbox;
  display: flex;
}

.e-bookmark-button {
  position: relative;
  top: 0;
}

.e-font {
  float: left;
}

.e-font-rtl {
  float: right;
}

.e-de-table-border-toptop-alignment, .e-de-table-border-topcenter-alignment, .e-de-table-border-topbottom-alignment, .e-de-table-border-diagionalup-alignment, .e-de-table-border-diagionaldown-alignment, .e-de-table-border-bottomleft-alignment, .e-de-table-border-bottomcenter-alignment, .e-de-table-border-bottomright-alignment {
  left: 48%;
  position: absolute;
  top: 59%;
  transform: translate(-50%, -50%);
}

.e-de-style-properties, .e-de-style-formatting {
  font-size: 14px;
  font-weight: 400;
}

.e-de-style-formatting {
  margin-bottom: 14px;
}

.e-de-style-paragraph-indent-group-button .e-btn, .e-de-style-paragraph-group-button .e-btn, .e-de-style-font-group-button .e-btn {
  box-shadow: none;
}

.e-de-style-properties {
  margin-bottom: 14px;
}

.e-de-style-nametype-div {
  margin-bottom: 14px;
}

.e-de-style-based-para-div {
  margin-bottom: 14px;
}

.e-de-style-name, .e-de-style-styletype, .e-de-style-style-based-on, .e-de-style-style-paragraph {
  font-size: 13px;
  font-weight: 400;
  margin-bottom: 8px;
  width: 180px;
}

.e-de-style-left-div {
  margin-right: 22px;
}

.e-de-style-left-div.e-de-rtl {
  margin-left: 22px;
  margin-right: 0;
}

.e-de-style-font-color-picker, .e-de-style-icon-button-size, .e-de-style-icon-button-first-size, .e-de-style-icon-button-last-size {
  height: 31px;
}

.e-bigger .e-de-style-font-color-picker, .e-bigger .e-de-style-icon-button-size, .e-bigger .e-de-style-icon-button-first-size, .e-bigger .e-de-style-icon-button-last-size {
  height: 35px;
}

.e-bigger .e-de-style-bold-button-size {
  height: 35px;
  margin-left: 6px;
  margin-right: 8px;
}

.e-de-style-bold-button-size {
  height: 31px;
  margin-left: 6px;
  margin-right: 8px;
}

.e-de-style-format-dropdwn .e-btn-icon {
  margin-left: 8px;
}

.e-de-style-font-color-picker, .e-de-style-icon-button-size {
  margin-right: 8px;
}

.e-de-style-icon-button-first-size {
  margin-left: 6px;
  margin-right: 3px;
}

.e-de-style-icon-button-last-size {
  margin-right: 6px;
}

.e-de-style-font-color-picker {
  margin-left: 8px;
}

.e-style-font-fmaily-right {
  margin-right: 8px;
}

.e-style-font {
  margin-left: 20px;
  margin-right: 20px;
}

.e-de-style-left-div .e-de-style-dlg-name-input {
  height: 31px;
}

.e-style-list {
  margin-left: 20px;
}

.e-de-style-dialog .e-de-style-only-this-document {
  margin-top: 25px;
}

.e-de-style-format-dropdwn {
  width: 135px;
}

.e-de-style-options-div {
  margin-bottom: 14px;
}

.e-de-style-paragraph-group-button {
  border-right: 2px solid #ced4da;
}

.e-de-style-font-group-button {
  border-left: 2px solid #ced4da;
  border-right: 2px solid #ced4da;
}

.e-de-op-replace-messagediv {
  color: #000;
  position: absolute;
  top: 144px;
}

.e-de-font-content-label .e-label, .e-de-font-dlg-cb-right .e-label, .e-de-font-checkbox .e-label {
  font-size: 12px;
}

.e-de-font-content-label .e-frame, .e-de-font-dlg-cb-right .e-frame, .e-de-font-checkbox .e-frame {
  height: 15px;
  line-height: 13px;
  width: 15px;
}

.e-de-op-input-group, .e-de-op-replacewith {
  height: auto;
}

.e-bigger .e-de-op-input-group, .e-bigger .e-de-op-replacewith {
  height: 40px;
}

.e-de-hyperlink-bookmark-check {
  margin-top: 20px;
}

.e-de-table-container-div .e-checkbox-wrapper .e-frame, .e-de-table-header-div .e-checkbox-wrapper .e-frame, .e-de-table-ppty-options-break .e-checkbox-wrapper .e-frame, .e-de-table-ppty-options-header-row .e-checkbox-wrapper .e-frame, .e-de-table-cell-header-div .e-checkbox-wrapper .e-frame, .e-de-tbl-btn-seperator .e-checkbox-wrapper .e-frame, .e-de-hyperlink-bookmark-check .e-checkbox-wrapper .e-frame, .e-de-tbl-margin-sub-header .e-frame {
  height: 15px;
  line-height: 13px;
  width: 15px;
}

.e-de-table-container-div .e-checkbox-wrapper .e-label, .e-de-table-header-div .e-checkbox-wrapper .e-label, .e-de-table-ppty-options-break .e-checkbox-wrapper .e-label, .e-de-table-ppty-options-header-row .e-checkbox-wrapper .e-label, .e-de-table-cell-header-div .e-checkbox-wrapper .e-label, .e-de-tbl-btn-seperator .e-checkbox-wrapper .e-label, .e-de-hyperlink-bookmark-check .e-checkbox-wrapper .e-label, .e-de-tbl-margin-sub-header .e-label {
  font-size: 14px;
}

.e-de-table-ppty-dlg-measure-div {
  float: right;
  margin-left: 20px;
  margin-top: -18px;
}

.e-de-table-ppty-dlg-measure-div.e-de-rtl {
  float: left;
  margin-left: 0;
  margin-right: 20px;
}

.e-de-table-ppty-dlg-measure-drop-down-div {
  float: right;
  margin-left: 20px;
  margin-top: 6px;
}

.e-de-table-ppty-dlg-measure-drop-down-div.e-de-rtl {
  margin-left: 0;
  margin-right: 20px;
}

.e-de-table-ppty-dlg-left-indent-container {
  bottom: 4px;
  left: 69px;
  position: relative;
}

.e-de-table-ppty-dlg-left-indent-container.e-de-rtl {
  right: 69px;
}

.e-de-table-ppty-dlg-row-height-label {
  float: right;
  margin-right: 184px;
  margin-top: -62px;
}

.e-de-table-ppty-dlg-row-height-label.e-de-rtl {
  float: left;
  margin-left: 184px;
  margin-right: 0;
}

.e-de-table-ppty-dlg-preferred-width-div {
  float: right;
  margin-left: 20px;
  margin-top: 6px;
}

.e-de-table-ppty-dlg-preferred-width-div.e-de-rtl {
  margin-left: 0;
  margin-right: 20px;
}

.e-de-table-ppty-options-break {
  margin-bottom: 8px;
}

.e-de-table-cell-subheader-div {
  margin-right: 125px;
  margin-top: -37px;
}

.e-de-table-cell-subheader-div.e-de-rtl {
  margin-left: 125px;
  margin-right: 0;
}

.e-de-table-ppty-dlg-cell-tab-measure-label {
  float: right;
  margin-right: 190px;
  margin-top: -58px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-table-ppty-dlg-row-header {
  padding-left: 12px;
  padding-right: 12px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-table-ppty-dlg-cell-header {
  padding-left: 12px;
  padding-right: 12px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-page-setup-dlg-margin-tab-header {
  padding-left: 15px;
  padding-right: 12px;
}

.e-styles-list {
  float: left;
  margin-right: 24px;
  width: 250px;
}

.e-styles-textboxdiv {
  padding-bottom: 15px;
}

.e-styles-listview .e-list-item {
  font-size: 13px;
  height: 30px;
  line-height: 27px;
}

.e-styles-common {
  padding-top: 5px;
}

.e-styles-button {
  float: right;
}

.e-de-toc-dlg-right-sub-container {
  margin-right: 2px;
  margin-top: 122px;
}

.e-de-toc-dlg-right-sub-container.e-de-rtl {
  margin-left: 2px;
  margin-right: 0;
}

.e-de-toc-dlg-right-sub-container.e-de-rtl {
  margin-right: 2px;
}

.e-de-toc-dlg-styles {
  margin-bottom: 11px;
  margin-left: -22px;
  margin-top: 20px;
}

.e-de-toc-dlg-styles.e-de-rtl {
  margin-left: 0;
  margin-right: -22px;
}

.e-de-toc-dlg-build-table {
  margin-top: 20px;
}

.e-de-toc-table-div .e-de-toc-dlg-toc-level {
  height: 24px;
  margin-left: 36px;
  width: 44px;
}

.e-de-toc-styles-table-div {
  border: 1px solid #c3c3c3;
  border-radius: 3px;
  margin-top: 15px;
  width: 213px;
}

.e-de-toc-dlg-sub-level-heading {
  font-size: 12px;
}

.e-de-toc-dlg-show-level-div {
  margin-left: 275px;
  margin-top: 16px;
}

.e-de-toc-dlg-show-level-div.e-de-rtl {
  margin-left: 0;
  margin-right: 275px;
}

.e-de-toc-table-div {
  height: 134px;
  overflow-y: scroll;
  width: 211px;
}

.e-de-toc-dlg-style-input {
  margin-bottom: 3px;
  margin-left: -22px;
  width: 210px;
}

.e-de-toc-dlg-outline-levels {
  margin-top: 10px;
  width: 150px;
}

.e-bookmark-textboxdiv .e-bookmark-textbox-input {
  height: 32px;
}

.e-styles-dlgfields {
  font-weight: 400;
  margin-bottom: 6px;
}

.e-de-toc-dlg-showlevel-div {
  margin-left: 276px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-op-find-tab-header {
  padding-left: 15px;
  padding-right: 0 25px 0 0;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-op-replace-tab-header {
  padding-left: 0 25px 0 0;
  padding-right: 0 25px 0 0;
}

.e-de-dlg-target .e-footer-content .e-list-dlg-font {
  margin-left: 1px;
}

.e-bookmark-dlgfields {
  font-weight: 400;
  margin-bottom: 8px;
}

.e-de-ui-wfloating-menu.e-de-ui-bullets-menu .e-de-ui-wfloating-menuitem-md {
  height: 65px;
  padding: 10px;
  width: 70px;
}

.e-de-ui-wfloating-menu.e-de-ui-bullets-menu .e-de-ui-wfloating-bullet-menuitem-md {
  height: 45px;
  width: 45px;
}

.e-de-bullet-icon-size {
  height: 45px;
  width: 45px;
}

.e-de-ui-list-header-presetmenu {
  cursor: pointer;
  font-size: 11px;
  line-height: 14px;
  min-width: 50px;
  overflow: hidden;
  text-align: left;
  white-space: nowrap;
  width: 100%;
}

.e-de-ui-bullet {
  font-size: 42px;
}

.e-de-ui-list-header-presetmenu .e-de-ui-list-line {
  border-bottom: 1px solid #ccc;
  margin-left: 5px;
  width: 100%;
}

.e-de-ui-list-header-presetmenu div span {
  color: #aaa;
  display: inline-block;
  vertical-align: middle;
}

.e-de-ui-wfloating-menu .e-de-ui-wfloating-menuitem, .e-de-ui-wfloating-menu .e-de-ui-menuitem-none {
  border: 0;
  box-shadow: inset 0 0 0 1px #ebebeb;
  cursor: pointer;
  height: 70px;
  margin: 0 5px 5px 0;
  padding: 0;
  width: 70px;
}

.e-de-ui-wfloating-menu {
  padding: 10px 4px 5px 10px;
}

.e-de-list-thumbnail .e-de-list-items {
  float: left;
}

.e-de-list-thumbnail .e-de-list-items {
  background: #fff;
  border: 1px solid transparent;
  clear: initial;
  display: inline-block;
  height: auto;
  margin: 5px;
  text-align: center;
  width: auto;
}

.e-de-list-items {
  background: #fff;
  box-sizing: border-box;
  cursor: pointer;
  list-style: none;
  padding: 7px 10px;
  position: relative;
}

.e-de-list-item-size {
  font-size: 14px;
}

.e-de-ui-wfloating-menu {
  padding: 10px 4px 5px 10px;
}

.e-de-table-border-fill.e-de-rtl {
  margin-left: 15px;
}

.e-de-table-border-fill:not(.e-de-rtl) {
  margin-right: 15px;
  margin-top: 5px;
}

.e-de-table-ppty-dlg-tabs {
  height: 280px;
  position: relative;
}

.e-de-ui-bullet-list-header-presetmenu .e-de-list-thumbnail .e-de-list-active, .e-de-style-numbered-list .e-de-list-thumbnail .e-de-list-active {
  border-color: #5a8e8a;
}

.e-de-bullet-icons {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-43%, -43%);
}

.e-de-header-footer-list {
  color: #5a8e8a;
}

.e-de-rtl-btn-div {
  font-size: 12px;
  margin-right: 14px;
  width: 200px;
}

.e-de-rtl-btn-div.e-de-rtl {
  margin-left: 14px;
  margin-right: 0;
}

.e-de-ltr-btn-div {
  font-size: 12px;
  width: 200px;
}

.e-de-tbl-rtl-btn-div {
  font-size: 12px;
  margin-right: 14px;
  width: 140px;
}

.e-de-tbl-rtl-btn-div.e-de-rtl {
  margin-left: 14px;
  margin-right: 0;
}

.e-de-tbl-ltr-btn-div {
  font-size: 12px;
  width: 140px;
}

.e-para-dlg-sub-height {
  height: 145px;
}

.e-de-disabledbutton {
  opacity: 0.4;
  pointer-events: none;
}

.e-bigger .e-de-insert-table {
  height: auto;
  width: auto;
}

.e-bigger .e-de-dlg-target .e-footer-content .e-control.e-btn.e-flat:not(.e-icon-btn) {
  height: auto;
}

.e-bigger .e-de-font-dlg {
  width: 375px;
}

.e-bigger .e-para-dlg-sub-height {
  height: 170px;
}

.e-bigger .e-de-toc-table-div .e-de-toc-dlg-toc-level.e-de-rtl {
  margin-right: 36px;
}

.e-bigger .e-de-font-content-label-width {
  width: 68px;
}

.e-bigger .e-de-toc-label {
  margin-left: 194px;
}

.e-bigger .e-de-toc-label-rtl {
  margin-left: 0;
  margin-right: 180px;
}

.e-bigger .e-de-outline-rtl {
  width: 173px;
}

.e-de-restrict-format {
  margin-top: 16px;
}

.e-de-rp-format {
  font-size: 13px;
  margin-bottom: 12px;
  opacity: .65;
}

.e-de-rp-checkbox {
  font-size: 12px;
}

.e-de-rp-border {
  margin-bottom: 12px;
  margin-top: 12px;
}

.e-de-rp-header {
  font-size: 15px;
  font-weight: 500;
  opacity: .87;
}

.e-de-restrict-pane .e-checkbox-wrapper .e-label {
  color: rgba(0, 0, 0, 0.87);
  font-size: 12px;
}

.e-de-restrict-pane .e-checkbox-wrapper .e-frame {
  height: 16px;
  width: 16px;
}

.e-de-restrict-pane .e-checkbox-wrapper {
  margin-bottom: 8px;
}

.e-de-rp-user .e-checkbox-wrapper {
  width: auto;
}

.e-de-rp-nav-btn, .e-de-rp-btn-enforce {
  background: #fafafa;
  border-radius: 2px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 2px 4px 0 rgba(0, 0, 0, 0.5);
  font-size: 13px;
  height: 36px;
  opacity: .87;
}

.e-de-rp-nav-btn {
  margin: 0 12px;
  width: 210px;
}

.e-de-rp-btn-stop-enforce {
  background: #fafafa;
  border-radius: 2px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 2px 4px 0 rgba(0, 0, 0, 0.5);
  font-size: 13px;
  height: 36px;
  margin: 0 46px;
  opacity: .87;
}

.e-de-rp-nav-lbl {
  font-size: 13px;
  margin: 0 28px;
}

.e-de-rp-sub-div {
  border-bottom: 1px solid #e4e4e4;
  padding: 12px;
}

.e-de-restrict-pane {
  padding-left: 0;
  padding-top: 0;
}

.e-de-rp-whole-header {
  padding: 12px;
}

.e-de-rp-user {
  background: #fff;
  border: 1px solid #e4e4e4;
  border-radius: 2px;
  font-size: 12px;
  height: 110px;
  width: 238px;
}

.e-de-rp-enforce {
  padding-top: 12px;
}

.e-de-rp-enforce-nav {
  margin: 12px 0;
}

.e-de-enforce-dlg-title {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 8px;
  margin-top: 8px;
}

.e-de-enforce .e-de-enforce-dlg-input {
  height: 32px;
  margin-bottom: 8px;
  width: 300px;
}

.e-de-user-add-btn {
  background: #ced4da;
  border-radius: 2px;
  width: 74px;
}

.e-de-user-dlg .e-de-user-dlg-textbox-input {
  margin-right: 16px;
  width: 304px;
}

.e-de-user-dlg-list {
  margin-bottom: 15px;
}

.e-de-user-listview {
  border: 1px solid #e4e4e4;
  border-radius: 2px;
  height: 106px;
}

.e-de-user-dlg-user {
  margin-bottom: 12px;
}

.e-user-delete {
  float: left;
}

.e-de-unprotect-dlg-title {
  font-size: 14px;
  margin-bottom: 8px;
}

.e-de-rp-stop-div1 {
  opacity: .87;
  padding: 12px 12px 6px 12px;
  font-weight: 500;
  border-bottom: 1px solid #E0E0E0;
}

.e-de-rp-stop-div2 {
  padding: 12px 12px 24px;
}

.e-de-rp-stop-div3 {
  padding: 0 0 12px 12px;
}

.e-de-rp-close-icon {
  float: right;
  position: relative;
  top: -7px;
}

.e-de-restrict-pane {
  height: 100%;
  overflow: auto;
  width: 268px;
}

.e-de-rp-nav-lbl {
  font-size: 13px;
  margin: 0 28px;
}

.e-documenteditor-optionspane {
  height: 100%;
}

.e-lib .e-js [class^='e-'], .e-lib .e-js [class*=' e-'] {
  box-sizing: content-box;
}
