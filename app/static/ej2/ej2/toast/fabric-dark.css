@charset "UTF-8";
.e-toast .e-toast-close-icon::before {
  content: "";
}

/*! toast layout */
.e-bigger .e-toast-container.e-toast-top-left,
.e-toast-container.e-bigger.e-toast-top-left {
  left: 10px;
  top: 10px;
}

.e-bigger .e-toast-container.e-toast-bottom-left,
.e-toast-container.e-bigger.e-toast-bottom-left {
  bottom: 10px;
  left: 10px;
}

.e-bigger .e-toast-container.e-toast-top-right,
.e-toast-container.e-bigger.e-toast-top-right {
  right: 10px;
  top: 10px;
}

.e-bigger .e-toast-container.e-toast-bottom-right,
.e-toast-container.e-bigger.e-toast-bottom-right {
  bottom: 10px;
  right: 10px;
}

.e-bigger .e-toast-container.e-toast-bottom-center,
.e-toast-container.e-bigger.e-toast-bottom-center {
  bottom: 10px;
}

.e-bigger .e-toast-container.e-toast-bottom-center .e-toast,
.e-toast-container.e-bigger.e-toast-bottom-center .e-toast {
  margin: 0 auto 10px;
}

.e-bigger .e-toast-container.e-toast-top-center,
.e-toast-container.e-bigger.e-toast-top-center {
  top: 10px;
}

.e-bigger .e-toast-container.e-toast-top-center .e-toast,
.e-toast-container.e-bigger.e-toast-top-center .e-toast {
  margin: 0 auto 10px;
}

.e-bigger .e-toast-container.e-toast-full-width,
.e-toast-container.e-bigger.e-toast-full-width {
  left: 0;
  right: 0;
}

.e-bigger .e-toast-container.e-toast-full-width .e-toast,
.e-toast-container.e-bigger.e-toast-full-width .e-toast {
  margin: 0 auto 10px;
}

.e-bigger .e-toast-container.e-rtl .e-toast .e-toast-message .e-toast-actions,
.e-toast-container.e-bigger.e-rtl .e-toast .e-toast-message .e-toast-actions {
  text-align: left;
}

.e-bigger .e-toast-container.e-rtl .e-toast .e-toast-message .e-toast-actions > *,
.e-toast-container.e-bigger.e-rtl .e-toast .e-toast-message .e-toast-actions > * {
  margin-left: initial;
  margin-right: 10px;
}

.e-bigger .e-toast-container.e-rtl .e-toast .e-toast-close-icon,
.e-toast-container.e-bigger.e-rtl .e-toast .e-toast-close-icon {
  margin-left: initial;
  margin-right: auto;
}

.e-bigger .e-toast-container.e-rtl .e-toast .e-toast-icon,
.e-toast-container.e-bigger.e-rtl .e-toast .e-toast-icon {
  margin-left: 8px;
  margin-right: initial;
}

.e-bigger .e-toast-container.e-rtl .e-toast .e-toast-progress,
.e-toast-container.e-bigger.e-rtl .e-toast .e-toast-progress {
  left: auto;
  right: 0;
}

.e-bigger .e-toast-container .e-toast,
.e-toast-container.e-bigger .e-toast {
  border-radius: 0;
  font-size: 14px;
  margin: 0 0 10px;
  min-height: 48px;
}

.e-bigger .e-toast-container .e-toast .e-toast-message .e-toast-content,
.e-toast-container.e-bigger .e-toast .e-toast-message .e-toast-content {
  padding: 8px 0 8px 0;
}

.e-bigger .e-toast-container .e-toast .e-toast-message .e-toast-content:first-child,
.e-toast-container.e-bigger .e-toast .e-toast-message .e-toast-content:first-child {
  padding: 0;
}

.e-bigger .e-toast-container .e-toast .e-toast-message .e-toast-content:last-child,
.e-toast-container.e-bigger .e-toast .e-toast-message .e-toast-content:last-child {
  padding-bottom: 0;
}

.e-bigger .e-toast-container .e-toast .e-toast-message .e-toast-content + .e-toast-actions,
.e-toast-container.e-bigger .e-toast .e-toast-message .e-toast-content + .e-toast-actions {
  padding: 0;
}

.e-bigger .e-toast-container .e-toast .e-toast-message .e-toast-actions,
.e-toast-container.e-bigger .e-toast .e-toast-message .e-toast-actions {
  padding: 8px 0 0 0;
  text-align: right;
}

.e-bigger .e-toast-container .e-toast .e-toast-message .e-toast-actions > *,
.e-toast-container.e-bigger .e-toast .e-toast-message .e-toast-actions > * {
  margin-left: 10px;
}

.e-bigger .e-toast-container .e-toast .e-toast-close-icon,
.e-toast-container.e-bigger .e-toast .e-toast-close-icon {
  font-size: 16px;
  height: 24px;
  width: 24px;
}

.e-bigger .e-toast-container .e-toast .e-toast-icon,
.e-toast-container.e-bigger .e-toast .e-toast-icon {
  font-size: 16px;
  height: 24px;
  margin-right: 8px;
  width: 24px;
}

.e-bigger .e-toast-container .e-toast .e-toast-progress,
.e-toast-container.e-bigger .e-toast .e-toast-progress {
  height: 2px;
}

.e-toast-container {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-direction: column;
      flex-direction: column;
  position: relative;
}

.e-toast-container.e-toast-top-left {
  left: 10px;
  top: 10px;
}

.e-toast-container.e-toast-bottom-left {
  bottom: 10px;
  left: 10px;
}

.e-toast-container.e-toast-top-right {
  right: 10px;
  top: 10px;
}

.e-toast-container.e-toast-bottom-right {
  bottom: 10px;
  right: 10px;
}

.e-toast-container.e-toast-bottom-center {
  bottom: 10px;
  pointer-events: none;
  right: 0;
  width: 100%;
}

.e-toast-container.e-toast-bottom-center .e-toast {
  margin: 0 auto 10px;
  pointer-events: auto;
}

.e-toast-container.e-toast-top-center {
  pointer-events: none;
  right: 0;
  top: 10px;
  width: 100%;
}

.e-toast-container.e-toast-top-center .e-toast {
  margin: 0 auto 10px;
  pointer-events: auto;
}

.e-toast-container.e-toast-full-width {
  left: 0;
  right: 0;
  width: 100%;
}

.e-toast-container.e-toast-full-width .e-toast {
  margin: 0 auto 10px;
  width: 96%;
}

.e-toast-container.e-rtl .e-toast .e-toast-actions {
  text-align: left;
}

.e-toast-container.e-rtl .e-toast .e-toast-actions > * {
  margin-left: initial;
  margin-right: 10px;
}

.e-toast-container.e-rtl .e-toast .e-toast-close-icon {
  margin-left: initial;
  margin-right: auto;
}

.e-toast-container.e-rtl .e-toast .e-toast-icon {
  margin-left: 8px;
  margin-right: initial;
}

.e-toast-container.e-rtl .e-toast .e-toast-progress {
  left: auto;
  right: 0;
}

.e-toast-container .e-toast {
  border-radius: 0;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 14px;
  margin: 0 0 10px;
  overflow: hidden;
  padding: 16px;
  position: relative;
}

.e-toast-container .e-toast .e-toast-icon,
.e-toast-container .e-toast .e-toast-message {
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.e-toast-container .e-toast > * {
  word-break: break-word;
  word-wrap: break-word;
}

.e-toast-container .e-toast .e-toast-message {
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
  -ms-flex-direction: column;
      flex-direction: column;
  overflow: hidden;
  width: inherit;
}

.e-toast-container .e-toast .e-toast-message .e-toast-title,
.e-toast-container .e-toast .e-toast-message .e-toast-content {
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-toast-container .e-toast .e-toast-message .e-toast-title:first-child,
.e-toast-container .e-toast .e-toast-message .e-toast-content:first-child {
  padding: 0;
}

.e-toast-container .e-toast .e-toast-message .e-toast-title:last-child,
.e-toast-container .e-toast .e-toast-message .e-toast-content:last-child {
  padding-bottom: 0;
}

.e-toast-container .e-toast .e-toast-message .e-toast-title > *,
.e-toast-container .e-toast .e-toast-message .e-toast-content > * {
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-toast-container .e-toast .e-toast-message .e-toast-title {
  font-size: 14px;
  font-weight: bold;
  letter-spacing: .5px;
}

.e-toast-container .e-toast .e-toast-message .e-toast-content {
  padding: 8px 0 8px 0;
  word-break: break-word;
  word-wrap: break-word;
}

.e-toast-container .e-toast .e-toast-message .e-toast-content + .e-toast-actions {
  padding-top: 0;
}

.e-toast-container .e-toast .e-toast-message .e-toast-actions {
  margin: 1px;
  padding: 8px 0 0 0;
  text-align: right;
}

.e-toast-container .e-toast .e-toast-message .e-toast-actions > * {
  margin-left: 10px;
}

.e-toast-container .e-toast .e-toast-close-icon {
  -ms-flex-align: center;
      align-items: center;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  height: 24px;
  -ms-flex-pack: center;
      justify-content: center;
  margin-left: auto;
  width: 24px;
}

.e-toast-container .e-toast .e-toast-icon {
  -ms-flex-align: center;
      align-items: center;
  font-size: 16px;
  height: 24px;
  -ms-flex-pack: center;
      justify-content: center;
  margin-right: 8px;
  width: 24px;
}

.e-toast-container .e-toast .e-toast-progress {
  bottom: 0;
  height: 2px;
  left: 0;
  position: absolute;
}

.e-content-placeholder.e-toast.e-placeholder-toast {
  background-size: 400px 100px;
  min-height: 100px;
}

.e-bigger .e-content-placeholder.e-toast.e-placeholder-toast,
.e-bigger.e-content-placeholder.e-toast.e-placeholder-toast {
  background-size: 400px 100px;
  min-height: 100px;
}

.e-toast-container .e-toast {
  background-color: #282727;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
  color: #dadada;
}

.e-toast-container .e-toast.e-toast-success {
  background-color: #37844d;
  color: #fff;
}

.e-toast-container .e-toast.e-toast-success .e-toast-message .e-toast-title {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-success .e-toast-message .e-toast-content {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-success .e-toast-icon {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-success:hover {
  background-color: #347d49;
  box-shadow: 0 3px 6px 0 rgba(248, 248, 248, 0.26);
  color: #fff;
}

.e-toast-container .e-toast.e-toast-info {
  background-color: #1e79cb;
  color: #fff;
}

.e-toast-container .e-toast.e-toast-info .e-toast-message .e-toast-title {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-info .e-toast-message .e-toast-content {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-info .e-toast-icon {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-info:hover {
  background-color: #1d74c2;
  box-shadow: 0 3px 6px 0 rgba(248, 248, 248, 0.26);
  color: #fff;
}

.e-toast-container .e-toast.e-toast-warning {
  background-color: #bf7500;
  color: #fff;
}

.e-toast-container .e-toast.e-toast-warning .e-toast-message .e-toast-title {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-warning .e-toast-message .e-toast-content {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-warning .e-toast-icon {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-warning:hover {
  background-color: b56f00;
  box-shadow: 0 3px 6px 0 rgba(248, 248, 248, 0.26);
  color: #fff;
}

.e-toast-container .e-toast.e-toast-danger {
  background-color: #cd2a19;
  color: #fff;
}

.e-toast-container .e-toast.e-toast-danger .e-toast-message .e-toast-title {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-danger .e-toast-message .e-toast-content {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-danger .e-toast-icon {
  color: #fff;
}

.e-toast-container .e-toast.e-toast-danger:hover {
  background-color: #c42818;
  box-shadow: 0 3px 6px 0 rgba(248, 248, 248, 0.26);
  color: #fff;
}

.e-toast-container .e-toast:hover {
  background-color: #333232;
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.26);
}

.e-toast-container .e-toast .e-toast-icon {
  color: #dadada;
}

.e-toast-container .e-toast .e-toast-close-icon {
  color: #dadada;
}

.e-toast-container .e-toast .e-toast-message .e-toast-title {
  color: #dadada;
}

.e-toast-container .e-toast .e-toast-message .e-toast-content {
  color: #dadada;
}

.e-toast-container .e-toast .e-toast-progress {
  background-color: #0074cc;
}
