@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
@keyframes e-input-ripple {
  100% {
    opacity: 0;
    transform: scale(4);
  }
}

.e-input-group-icon.e-spin-up::before {
  content: '\e834';
  font-family: 'e-icons';
}

.e-input-group-icon.e-spin-down::before {
  content: '\e83d';
  font-family: 'e-icons';
}

.e-numeric-container {
  width: 100%;
}

.e-content-placeholder.e-numeric.e-placeholder-numeric {
  background-size: 300px 33px;
  min-height: 33px;
}

.e-bigger.e-content-placeholder.e-numeric.e-placeholder-numeric,
.e-bigger .e-content-placeholder.e-numeric.e-placeholder-numeric {
  background-size: 300px 40px;
  min-height: 40px;
}

.e-numeric.e-control-wrapper.e-input-group .e-input-group-icon {
  font-size: 12px;
}

.e-bigger .e-control-wrapper.e-numeric.e-input-group .e-input-group-icon,
.e-bigger.e-control-wrapper.e-numeric.e-input-group .e-input-group-icon {
  font-size: 12px;
}
