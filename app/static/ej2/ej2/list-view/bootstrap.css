.e-icon-collapsible::before {
  content: '\e943';
}

.e-icon-back::before {
  content: '\e94b';
  font-size: 14px;
}

.e-bigger .e-listview .e-list-item,
.e-listview.e-bigger .e-list-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-left: 0 solid transparent;
  border-right: 0 solid transparent;
  border-top: 0 solid transparent;
}

.e-bigger .e-listview:not(.e-list-template) .e-list-item,
.e-listview.e-bigger:not(.e-list-template) .e-list-item {
  height: 48px;
  line-height: 45px;
  position: relative;
}

.e-bigger .e-listview .e-text-content,
.e-listview.e-bigger .e-text-content {
  font-size: 15px;
}

.e-bigger .e-listview .e-list-group-item,
.e-listview.e-bigger .e-list-group-item {
  height: 48px;
  line-height: 45px;
}

.e-bigger .e-listview .e-list-header,
.e-listview.e-bigger .e-list-header {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  font-weight: bold;
  height: 48px;
}

.e-bigger .e-listview .e-list-header .e-text.header,
.e-listview.e-bigger .e-list-header .e-text.header {
  display: none;
}

.e-bigger .e-listview .e-list-header .e-headertemplate-text.nested-header,
.e-listview.e-bigger .e-list-header .e-headertemplate-text.nested-header {
  display: none;
}

.e-bigger .e-listview .e-list-header .e-text,
.e-listview.e-bigger .e-list-header .e-text {
  font-size: 15px;
}

.e-listview {
  -webkit-overflow-scrolling: touch;
  border: 0 solid;
  border-color: none;
  border-radius: 0;
  display: block;
  overflow: auto;
  position: relative;
  width: 100%;
}

.e-listview:not(.e-list-template) .e-list-item {
  height: 40px;
  line-height: 36px;
  padding: 0 15px;
  position: relative;
}

.e-listview .e-list-item {
  border-bottom: 0 solid;
}

.e-listview .e-list-parent {
  margin: 0;
  padding: 0;
}

.e-listview .e-list-header .e-text.header {
  display: none;
}

.e-listview .e-icon-back {
  margin-top: -2px;
}

.e-listview .e-list-header .e-headertemplate-text.nested-header {
  display: none;
}

.e-listview .e-list-header {
  -ms-flex-align: center;
      align-items: center;
  border-bottom: 1px solid;
  display: -ms-flexbox;
  display: flex;
  font-weight: bold;
  height: 40px;
  padding: 0 20px;
}

.e-listview .e-has-header > .e-view {
  top: 45px;
}

.e-listview .e-but-back {
  cursor: pointer;
  padding-right: 10px;
}

.e-listview .e-list-group-item:first-child {
  border: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.e-listview .e-list-group-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0 solid;
  font-weight: 600;
  height: 40px;
  line-height: 36px;
  padding: 0 15px;
}

.e-listview .e-icon-collapsible {
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  right: 0%;
  top: 50%;
  transform: translateY(-50%);
}

.e-listview .e-text-content {
  height: 100%;
  position: relative;
  vertical-align: middle;
}

.e-listview .e-text-content * {
  display: inline-block;
  vertical-align: middle;
}

.e-listview .e-text-content.e-checkbox .e-list-text {
  width: calc(100% - 40px);
}

.e-listview .e-text-content.e-checkbox.e-checkbox-left .e-list-icon + .e-list-text {
  width: calc(100% - 90px);
}

.e-listview .e-text-content.e-checkbox.e-checkbox-right .e-list-icon + .e-list-text {
  width: calc(100% - 80px);
}

.e-listview .e-list-item.e-checklist.e-has-child .e-text-content.e-checkbox.e-checkbox-right .e-list-icon + .e-list-text {
  width: calc(100% - 92px);
}

.e-listview .e-checkbox .e-checkbox-left {
  margin: -4px 10px 0 0;
}

.e-listview .e-checkbox .e-checkbox-right {
  margin: -4px 0 0 10px;
}

.e-listview .e-list-text {
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  white-space: nowrap;
  width: 100%;
}

.e-listview .e-list-icon + .e-list-text {
  width: calc(100% - 60px);
}

.e-listview .e-icon-wrapper .e-list-text {
  width: calc(100% - 60px);
}

.e-listview .e-icon-wrapper.e-text-content.e-checkbox .e-list-text {
  width: calc(100% - 60px);
}

.e-listview .e-list-icon {
  height: 30px;
  margin-right: 10px;
  width: 30px;
}

.e-listview .e-content {
  overflow: hidden;
  position: relative;
}

.e-listview .e-list-header .e-text {
  cursor: pointer;
  text-indent: 0;
}

.e-listview .e-text .e-headertext {
  display: inline-block;
  line-height: inherit;
}

.e-listview.e-rtl {
  direction: rtl;
}

.e-listview.e-rtl .e-list-icon {
  margin-left: 16px;
  margin-right: 0;
}

.e-listview.e-rtl .e-icon-collapsible {
  left: 0%;
  right: initial;
  top: 50%;
  transform: translateY(-50%) rotate(180deg);
}

.e-listview.e-rtl .e-list-header .e-text {
  cursor: pointer;
}

.e-listview.e-rtl .e-but-back {
  transform: rotate(180deg);
}

.e-listview.e-rtl .e-icon-back {
  margin-top: -1px;
}

.e-listview.e-rtl .e-checkbox .e-checkbox-left {
  margin: -4px 0 0 10px;
}

.e-listview.e-rtl .e-checkbox .e-checkbox-right {
  margin: -4px 10px 0 0;
}

.e-listview {
  border-color: rgba(0, 0, 0, 0.1);
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
}

.e-listview .e-list-header {
  background-color: #fff;
  border-color: rgba(0, 0, 0, 0.12);
  color: rgba(51, 51, 51, 0.95);
  font-size: 14px;
}

.e-listview .e-icons {
  color: rgba(0, 0, 0, 0.75);
}

.e-listview .e-list-item {
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-left: 0 solid transparent;
  border-right: 0 solid transparent;
  border-top: 0 solid transparent;
  color: #333333;
}

.e-listview .e-list-item.e-hover,
.e-listview .e-list-item.e-hover.e-active.e-checklist {
  background-color: #f5f5f5;
  border-color: transparent;
  color: rgba(51, 51, 51, 0.87);
}

.e-listview .e-list-item.e-active {
  background-color: #e6e6e6;
  color: rgba(51, 51, 51, 0.87);
}

.e-listview .e-list-item.e-active.e-checklist {
  background-color: #fff;
  color: #333333;
}

.e-listview .e-list-item.e-focused,
.e-listview .e-list-item.e-focused.e-active.e-checklist {
  background-color: #e6e6e6;
  color: rgba(51, 51, 51, 0.87);
}

.e-listview .e-list-item.e-focused .e-checkbox-wrapper .e-frame.e-check,
.e-listview .e-list-item.e-focused .e-css.e-checkbox-wrapper .e-frame.e-check {
  background-color: #fff;
  border-color: #ccc;
  color: #555;
}

.e-listview .e-list-group-item {
  background-color: #fff;
  border-color: rgba(0, 0, 0, 0.12);
  color: #333;
  font-size: 14px;
}

.e-listview.e-list-template .e-list-wrapper {
  height: inherit;
  position: relative;
}

.e-listview.e-list-template .e-list-wrapper:not(.e-list-multi-line) {
  padding: 0.7142em 1.0714em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-multi-line {
  padding: 0.7142em 1.0714em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-multi-line .e-list-item-header {
  color: #333;
  display: block;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  overflow: hidden;
  padding: 0.038em 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-listview.e-list-template .e-list-wrapper.e-list-multi-line .e-list-content {
  color: #777;
  display: block;
  font-size: 14px;
  margin: 0;
  padding: 0.038em 0;
  word-wrap: break-word;
}

.e-listview.e-list-template .e-list-wrapper.e-list-multi-line .e-list-content:not(.e-text-overflow) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-listview.e-list-template .e-list-wrapper.e-list-avatar .e-avatar {
  height: 2.8571em;
  left: 1.0667em;
  position: absolute;
  top: 0.7142em;
  width: 2.8571em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-avatar:not(.e-list-badge) {
  padding-left: 4.6428em;
  padding-right: 1.0714em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) {
  padding-left: 1.0714em;
  padding-right: 4.6428em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) .e-avatar {
  height: 2.8571em;
  position: absolute;
  right: 1.0667em;
  top: 0.7142em;
  width: 2.8571em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-multi-line.e-list-avatar .e-avatar {
  top: 0.7142em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-multi-line.e-list-avatar-right:not(.e-list-badge) .e-avatar {
  top: 0.7142em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-badge .e-badge {
  font-size: 15px;
  height: 1.333em;
  line-height: 1.433em;
  padding: 0;
  position: absolute;
  right: 1em;
  top: 50%;
  transform: translateY(-50%);
  width: 2em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-badge.e-list-avatar {
  padding-left: 4.6428em;
  padding-right: 3.9285em;
}

.e-listview.e-list-template .e-list-wrapper.e-list-badge:not(.e-list-avatar) {
  padding-left: 1.0714em;
  padding-right: 3.9285em;
}

.e-listview.e-list-template .e-list-wrapper:not(.e-list-multi-line) .e-list-content {
  display: block;
  margin: 0;
  overflow: hidden;
  padding: 0.83em 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-listview.e-list-template .e-list-item.e-hover .e-list-item-header {
  color: rgba(51, 51, 51, 0.87);
}

.e-listview.e-list-template .e-list-item.e-hover .e-list-content {
  color: rgba(51, 51, 51, 0.87);
}

.e-listview.e-list-template .e-list-item.e-active .e-list-item-header {
  color: rgba(51, 51, 51, 0.87);
}

.e-listview.e-list-template .e-list-item.e-active .e-list-content {
  color: rgba(51, 51, 51, 0.87);
}

.e-listview.e-rtl.e-list-template .e-list-wrapper.e-list-avatar .e-avatar {
  left: inherit;
  right: 1.0667em;
}

.e-listview.e-rtl.e-list-template .e-list-wrapper.e-list-avatar:not(.e-list-badge) {
  padding-left: 1.0714em;
  padding-right: 4.6428em;
}

.e-listview.e-rtl.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) {
  padding-left: 4.6428em;
  padding-right: 1.0714em;
}

.e-listview.e-rtl.e-list-template .e-list-wrapper.e-list-avatar-right:not(.e-list-badge) .e-avatar {
  left: 1.0667em;
  right: inherit;
}

.e-listview.e-rtl.e-list-template .e-list-wrapper.e-list-badge .e-badge {
  left: 1em;
  right: inherit;
}

.e-listview.e-rtl.e-list-template .e-list-wrapper.e-list-badge.e-list-avatar {
  padding-left: 3.9285em;
  padding-right: 4.6428em;
}

.e-listview.e-rtl.e-list-template .e-list-wrapper.e-list-badge:not(.e-list-avatar) {
  padding-left: 3.9285em;
  padding-right: 1.0714em;
}
