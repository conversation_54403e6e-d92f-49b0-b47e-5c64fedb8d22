@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
/*! component's theme wise override material-definitions and variables */
.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-east, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-east {
  height: 100%;
  padding: 20px 0;
  right: 1px;
  top: 0;
  width: 12px;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-west, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-west {
  height: 100%;
  left: 0;
  padding: 20px 0;
  top: 0;
  width: 12px;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-north, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-north {
  height: 12px;
  padding: 0 20px;
  top: 1px;
  width: 100%;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south {
  bottom: 1px;
  height: 12px;
  padding: 0 20px;
  width: 100%;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south-east, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south-east {
  bottom: 0;
  right: 1px;
  z-index: 10;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-north-west, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-north-west {
  left: 2px;
  top: 2px;
  z-index: 10;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-north-east, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-north-east {
  right: 2px;
  top: 2px;
  z-index: 10;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south-west, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south-west {
  bottom: 1px;
  left: 1px;
  z-index: 10;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south-east::before, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south-east::before {
  bottom: 0;
  content: '\eb05';
  font-size: 12px;
  position: absolute;
  right: 0;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south-west::before, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south-west::before {
  bottom: 0;
  content: '\eb05';
  font-size: 12px;
  left: 0;
  position: absolute;
  transform: rotateY(180deg);
}

.e-dashboardlayout.e-control .e-dashboard-gridline-table {
  background: #1E1E1E;
  border-collapse: collapse;
  height: 100%;
  width: 100%;
}

.e-dashboardlayout.e-control .e-dashboard-gridline-table tbody tr td.e-dashboard-gridline {
  border: 1px dotted #424242;
  position: absolute;
}

.e-dashboardlayout.e-control.e-responsive {
  width: 100% !important;
}

.e-dashboardlayout.e-control.e-prevent {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-dashboardlayout.e-control .e-panel {
  border-radius: 2px;
}

.e-dashboardlayout.e-control .e-panel:hover {
  border: 1px #9e9e9e solid;
}

.e-dashboardlayout.e-control .e-panel:hover .e-panel-container .e-resize {
  display: block;
}

.e-dashboardlayout.e-control .e-panel:active {
  border: 1px #00b0ff solid;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize {
  display: none;
}

.e-dashboardlayout.e-control .e-panel.e-panel-transition {
  transition: top .5s, left .5s;
}

.e-dashboardlayout.e-control .e-panel .e-panel-header {
  border-bottom: none;
  color: rgba(255, 255, 255, 0.7);
  font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif", "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 500;
  height: 28px;
  padding: 2px 12px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-header div {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-dashboardlayout.e-control .e-panel .e-panel-header .e-header-content {
  display: inline-block;
}

.e-dashboardlayout.e-control .e-panel .e-panel-header .e-header-template {
  float: right;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container {
  height: 100%;
  width: 100%;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-panel-content .e-blazor-template {
  height: inherit;
  width: inherit;
}

.e-dashboardlayout.e-control .e-panel {
  border: 1px #616161 solid;
  height: 100%;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single, .e-dashboardlayout.e-control .e-panel .e-resize.e-double {
  position: absolute;
  -ms-touch-action: none;
      touch-action: none;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-east, .e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-west, .e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-north, .e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-east, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-west, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-north, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south {
  border: none;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-east:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-east:hover {
  cursor: e-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-west:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-west:hover {
  cursor: w-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-north:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-north:hover {
  cursor: n-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south:hover {
  cursor: s-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-north-west:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-north-west:hover {
  cursor: nw-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-north-east:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-north-east:hover {
  cursor: ne-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south-west:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south-west:hover {
  cursor: sw-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single.e-south-east:hover, .e-dashboardlayout.e-control .e-panel .e-resize.e-double.e-south-east:hover {
  cursor: se-resize;
}

.e-dashboardlayout.e-control .e-panel .e-resize.e-single::before, .e-dashboardlayout.e-control .e-panel .e-resize.e-double::before {
  font-family: 'e-icons';
  position: absolute;
}

.e-dashboardlayout.e-control .e-panel.e-draggable:hover {
  cursor: move;
}

.e-dashboardlayout.e-control .e-panel.e-dragging, .e-dashboardlayout.e-control .e-panel.e-item-moving {
  cursor: move;
  z-index: 1111;
}

.e-dashboardlayout.e-control .e-panel.e-rtl .e-panel-header .e-header-template {
  float: left;
}

.e-dashboardlayout.e-control .e-holder {
  background: rgba(0, 176, 255, 0.25);
  border: 2px rgba(0, 176, 255, 0.25) dotted;
  border-radius: 0;
  position: absolute;
  border-radius: 2px;
}

.e-dashboardlayout.e-control .e-holder.e-holder-transition {
  transition: top .3s, left .3s;
}

.e-dashboardlayout.e-control .e-panel.e-bigger.e-panel-header {
  font-size: 13px;
  font-weight: 500;
  height: 30;
  padding: 0 12px;
}

.e-content-placeholder.e-dashboardlayout.e-placeholder-dashboardlayout {
  height: 100%;
  width: 100%;
}

.e-dashboardlayout.e-control {
  display: block;
  position: relative;
}

.e-dashboardlayout.e-control .e-panel {
  background: #303030;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  position: absolute;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-panel-header {
  color: rgba(255, 255, 255, 0.7);
}

.e-dashboardlayout.e-control .e-panel {
  background: #303030;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  position: absolute;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-panel-header {
  color: rgba(255, 255, 255, 0.7);
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-single {
  background: none;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double {
  color: rgba(255, 255, 255, 0.7);
  font-size: 8px;
  height: 16px;
  width: 16px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-south-east-double {
  bottom: -5px;
  right: -6px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-south-west-double {
  bottom: -5px;
  left: -6px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-north-west-double {
  left: -6px;
  top: -5px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-north-east-double {
  right: -6px;
  top: -5px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-north-east-shrink {
  right: -6px;
  top: -5px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-north-west-shrink {
  left: -6px;
  top: -5px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-south-west-shrink {
  bottom: -5px;
  left: -6px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-south-east-shrink {
  bottom: -5px;
  right: -6px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-north-east-expand {
  right: -6px;
  top: -5px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-north-west-expand {
  left: -6px;
  top: -5px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-south-west-expand {
  bottom: -5px;
  left: -6px;
}

.e-dashboardlayout.e-control .e-panel .e-panel-container .e-resize.e-double.e-south-east-expand {
  bottom: -5px;
  right: -6px;
}

.e-dashboardlayout.e-control .e-panel.e-bigger .e-panel-header {
  color: rgba(255, 255, 255, 0.7);
}

.e-lib .e-js [class^='e-'], .e-lib .e-js [class*=' e-'] {
  box-sizing: content-box;
}
