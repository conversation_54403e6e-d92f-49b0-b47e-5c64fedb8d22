@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
.e-checkbox-wrapper .e-check::before, .e-css.e-checkbox-wrapper .e-check::before {
  content: '\e933';
}

.e-checkbox-wrapper .e-stop::before, .e-css.e-checkbox-wrapper .e-stop::before {
  content: '\e934';
}

/*! checkbox layout */
.e-checkbox-wrapper, .e-css.e-checkbox-wrapper {
  cursor: pointer;
  display: inline-block;
  line-height: 1;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-checkbox-wrapper label, .e-css.e-checkbox-wrapper label {
  cursor: pointer;
  display: inline-block;
  line-height: 0;
  margin: 0;
  position: relative;
  white-space: nowrap;
}

.e-checkbox-wrapper:focus .e-frame, .e-css.e-checkbox-wrapper:focus .e-frame {
  box-shadow: none;
}

.e-checkbox-wrapper .e-ripple-container, .e-css.e-checkbox-wrapper .e-ripple-container {
  border-radius: 50%;
  bottom: -9px;
  height: 36px;
  left: -9px;
  pointer-events: none;
  position: absolute;
  right: -9px;
  top: -9px;
  width: 36px;
  z-index: 1;
}

.e-checkbox-wrapper .e-label, .e-css.e-checkbox-wrapper .e-label {
  cursor: pointer;
  display: inline-block;
  font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif", "-apple-system", "BlinkMacSystemFont";
  font-size: 13px;
  font-weight: normal;
  line-height: 18px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
  white-space: normal;
}

.e-checkbox-wrapper .e-checkbox, .e-css.e-checkbox-wrapper .e-checkbox {
  height: 1px;
  opacity: 0;
  position: absolute;
  width: 1px;
}

.e-checkbox-wrapper .e-checkbox + .e-label, .e-css.e-checkbox-wrapper .e-checkbox + .e-label {
  margin-right: 10px;
}

.e-checkbox-wrapper .e-frame, .e-css.e-checkbox-wrapper .e-frame {
  border: 2px solid;
  border-radius: 2px;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-family: 'e-icons';
  height: 18px;
  line-height: 10px;
  padding: 2px 0;
  text-align: center;
  vertical-align: middle;
  width: 18px;
}

.e-checkbox-wrapper .e-frame + .e-label, .e-css.e-checkbox-wrapper .e-frame + .e-label {
  margin-left: 10px;
}

.e-checkbox-wrapper .e-frame + .e-ripple-container, .e-css.e-checkbox-wrapper .e-frame + .e-ripple-container {
  left: auto;
}

.e-checkbox-wrapper .e-check, .e-css.e-checkbox-wrapper .e-check {
  font-size: 12px;
}

.e-checkbox-wrapper .e-stop, .e-css.e-checkbox-wrapper .e-stop {
  font-size: 10px;
  line-height: 10px;
}

.e-checkbox-wrapper.e-checkbox-disabled, .e-css.e-checkbox-wrapper.e-checkbox-disabled {
  cursor: default;
  pointer-events: none;
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame {
  cursor: default;
}

.e-checkbox-wrapper.e-checkbox-disabled .e-label, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-label {
  cursor: default;
}

.e-checkbox-wrapper.e-rtl .e-ripple-container, .e-css.e-checkbox-wrapper.e-rtl .e-ripple-container {
  right: -9px;
}

.e-checkbox-wrapper.e-rtl .e-frame, .e-css.e-checkbox-wrapper.e-rtl .e-frame {
  margin: 0;
}

.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container {
  left: -9px;
  right: auto;
}

.e-checkbox-wrapper.e-rtl .e-label, .e-css.e-checkbox-wrapper.e-rtl .e-label {
  margin-left: 0;
  margin-right: 10px;
}

.e-checkbox-wrapper.e-rtl .e-label + .e-frame, .e-css.e-checkbox-wrapper.e-rtl .e-label + .e-frame {
  margin: 0;
}

.e-checkbox-wrapper.e-rtl .e-checkbox + .e-label, .e-css.e-checkbox-wrapper.e-rtl .e-checkbox + .e-label {
  margin-left: 10px;
  margin-right: 0;
}

.e-checkbox-wrapper.e-small .e-frame, .e-css.e-checkbox-wrapper.e-small .e-frame {
  height: 14px;
  line-height: 6px;
  width: 14px;
}

.e-checkbox-wrapper.e-small .e-check, .e-css.e-checkbox-wrapper.e-small .e-check {
  font-size: 10px;
}

.e-checkbox-wrapper.e-small .e-stop, .e-css.e-checkbox-wrapper.e-small .e-stop {
  font-size: 8px;
  line-height: 6px;
}

.e-checkbox-wrapper.e-small .e-label, .e-css.e-checkbox-wrapper.e-small .e-label {
  font-size: 13px;
  line-height: 14px;
}

.e-checkbox-wrapper.e-small .e-ripple-container, .e-css.e-checkbox-wrapper.e-small .e-ripple-container {
  bottom: -9px;
  height: 32px;
  left: -9px;
  right: -9px;
  top: -9px;
  width: 32px;
}

.e-small .e-checkbox-wrapper .e-frame, .e-small.e-checkbox-wrapper .e-frame, .e-small .e-css.e-checkbox-wrapper .e-frame, .e-small.e-css.e-checkbox-wrapper .e-frame {
  height: 14px;
  line-height: 6px;
  width: 14px;
}

.e-small .e-checkbox-wrapper .e-check, .e-small.e-checkbox-wrapper .e-check, .e-small .e-css.e-checkbox-wrapper .e-check, .e-small.e-css.e-checkbox-wrapper .e-check {
  font-size: 10px;
}

.e-small .e-checkbox-wrapper .e-stop, .e-small.e-checkbox-wrapper .e-stop, .e-small .e-css.e-checkbox-wrapper .e-stop, .e-small.e-css.e-checkbox-wrapper .e-stop {
  font-size: 8px;
  line-height: 6px;
}

.e-small .e-checkbox-wrapper .e-label, .e-small.e-checkbox-wrapper .e-label, .e-small .e-css.e-checkbox-wrapper .e-label, .e-small.e-css.e-checkbox-wrapper .e-label {
  font-size: 13px;
  line-height: 14px;
}

.e-small .e-checkbox-wrapper .e-ripple-container, .e-small.e-checkbox-wrapper .e-ripple-container, .e-small .e-css.e-checkbox-wrapper .e-ripple-container, .e-small.e-css.e-checkbox-wrapper .e-ripple-container {
  bottom: -9px;
  height: 32px;
  left: -9px;
  right: -9px;
  top: -9px;
  width: 32px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-frame, .e-bigger.e-small.e-checkbox-wrapper .e-frame, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-frame, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-frame {
  height: 20px;
  line-height: 12px;
  width: 20px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-check, .e-bigger.e-small.e-checkbox-wrapper .e-check, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-check, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-check {
  font-size: 12px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-stop, .e-bigger.e-small.e-checkbox-wrapper .e-stop, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-stop, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-stop {
  font-size: 10px;
  line-height: 12px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-label, .e-bigger.e-small.e-checkbox-wrapper .e-label, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-label, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-label {
  font-size: 14px;
  line-height: 20px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-ripple-container, .e-bigger.e-small.e-checkbox-wrapper .e-ripple-container, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-ripple-container, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-ripple-container {
  bottom: -9px;
  height: 38px;
  left: -9px;
  right: -9px;
  top: -9px;
  width: 38px;
}

.e-bigger .e-checkbox-wrapper .e-frame, .e-bigger.e-checkbox-wrapper .e-frame, .e-bigger .e-css.e-checkbox-wrapper .e-frame, .e-bigger.e-css.e-checkbox-wrapper .e-frame {
  height: 22px;
  line-height: 14px;
  width: 22px;
}

.e-bigger .e-checkbox-wrapper .e-frame + .e-label, .e-bigger.e-checkbox-wrapper .e-frame + .e-label, .e-bigger .e-css.e-checkbox-wrapper .e-frame + .e-label, .e-bigger.e-css.e-checkbox-wrapper .e-frame + .e-label {
  font-size: 14px;
  line-height: 22px;
  margin-left: 12px;
}

.e-bigger .e-checkbox-wrapper .e-check, .e-bigger.e-checkbox-wrapper .e-check, .e-bigger .e-css.e-checkbox-wrapper .e-check, .e-bigger.e-css.e-checkbox-wrapper .e-check {
  font-size: 16px;
}

.e-bigger .e-checkbox-wrapper .e-stop, .e-bigger.e-checkbox-wrapper .e-stop, .e-bigger .e-css.e-checkbox-wrapper .e-stop, .e-bigger.e-css.e-checkbox-wrapper .e-stop {
  font-size: 12px;
  line-height: 14px;
}

.e-bigger .e-checkbox-wrapper .e-label, .e-bigger.e-checkbox-wrapper .e-label, .e-bigger .e-css.e-checkbox-wrapper .e-label, .e-bigger.e-css.e-checkbox-wrapper .e-label {
  font-size: 14px;
}

.e-bigger .e-checkbox-wrapper .e-ripple-container, .e-bigger.e-checkbox-wrapper .e-ripple-container, .e-bigger .e-css.e-checkbox-wrapper .e-ripple-container, .e-bigger.e-css.e-checkbox-wrapper .e-ripple-container {
  bottom: -9px;
  height: 40px;
  left: -9px;
  right: -9px;
  top: -9px;
  width: 40px;
}

.e-bigger .e-checkbox-wrapper.e-rtl .e-frame, .e-bigger.e-checkbox-wrapper.e-rtl .e-frame, .e-bigger .e-css.e-checkbox-wrapper.e-rtl .e-frame, .e-bigger.e-css.e-checkbox-wrapper.e-rtl .e-frame {
  margin: 0;
}

.e-bigger .e-checkbox-wrapper.e-rtl .e-frame + .e-label, .e-bigger.e-checkbox-wrapper.e-rtl .e-frame + .e-label, .e-bigger .e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-label, .e-bigger.e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-label {
  margin-left: 0;
  margin-right: 12px;
}

.e-bigger .e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-bigger.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-bigger .e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-bigger.e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container {
  right: auto;
}

.e-bigger .e-checkbox-wrapper.e-small .e-frame, .e-bigger.e-checkbox-wrapper.e-small .e-frame, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-frame, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-frame {
  height: 20px;
  line-height: 12px;
  width: 20px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-check, .e-bigger.e-checkbox-wrapper.e-small .e-check, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-check, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-check {
  font-size: 12px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-stop, .e-bigger.e-checkbox-wrapper.e-small .e-stop, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-stop, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-stop {
  font-size: 10px;
  line-height: 12px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-label, .e-bigger.e-checkbox-wrapper.e-small .e-label, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-label, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-label {
  font-size: 14px;
  line-height: 20px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-ripple-container, .e-bigger.e-checkbox-wrapper.e-small .e-ripple-container, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-ripple-container, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-ripple-container {
  bottom: -9px;
  height: 38px;
  left: -9px;
  right: -9px;
  top: -9px;
  width: 38px;
}

/*! checkbox theme */
.e-checkbox-wrapper, .e-css.e-checkbox-wrapper {
  -webkit-tap-highlight-color: transparent;
}

.e-checkbox-wrapper .e-frame, .e-css.e-checkbox-wrapper .e-frame {
  background-color: none;
  border-color: rgba(255, 255, 255, 0.7);
}

.e-checkbox-wrapper .e-frame.e-check, .e-css.e-checkbox-wrapper .e-frame.e-check {
  background-color: #00b0ff;
  border-color: transparent;
  color: #000;
}

.e-checkbox-wrapper .e-frame.e-stop, .e-css.e-checkbox-wrapper .e-frame.e-stop {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.7);
  color: rgba(255, 255, 255, 0.7);
}

.e-checkbox-wrapper .e-ripple-element, .e-css.e-checkbox-wrapper .e-ripple-element {
  background: rgba(0, 176, 255, 0.26);
}

.e-checkbox-wrapper .e-ripple-check .e-ripple-element, .e-css.e-checkbox-wrapper .e-ripple-check .e-ripple-element {
  background: rgba(255, 255, 255, 0.12);
}

.e-checkbox-wrapper:active .e-ripple-element, .e-css.e-checkbox-wrapper:active .e-ripple-element {
  background: rgba(255, 255, 255, 0.12);
}

.e-checkbox-wrapper:active .e-ripple-check .e-ripple-element, .e-css.e-checkbox-wrapper:active .e-ripple-check .e-ripple-element {
  background: rgba(0, 176, 255, 0.26);
}

.e-checkbox-wrapper .e-label, .e-css.e-checkbox-wrapper .e-label {
  color: #fff;
}

.e-checkbox-wrapper .e-checkbox:focus + .e-frame, .e-css.e-checkbox-wrapper .e-checkbox:focus + .e-frame {
  background-color: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.12);
  box-shadow: none;
}

.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-check, .e-css.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-check {
  background-color: #00b0ff;
  border-color: transparent;
  box-shadow: none;
  color: #000;
}

.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-stop, .e-css.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-stop {
  box-shadow: none;
  color: rgba(255, 255, 255, 0.7);
}

.e-checkbox-wrapper:hover .e-frame, .e-css.e-checkbox-wrapper:hover .e-frame {
  background-color: none;
  border-color: rgba(255, 255, 255, 0.7);
}

.e-checkbox-wrapper:hover .e-frame.e-check, .e-css.e-checkbox-wrapper:hover .e-frame.e-check {
  background-color: #00b0ff;
  border-color: transparent;
  color: #000;
}

.e-checkbox-wrapper:hover .e-frame.e-stop, .e-css.e-checkbox-wrapper:hover .e-frame.e-stop {
  color: rgba(255, 255, 255, 0.7);
}

.e-checkbox-wrapper:hover .e-label, .e-css.e-checkbox-wrapper:hover .e-label {
  color: rgba(255, 255, 255, 0.7);
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.3);
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-check, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-check {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: transparent;
  color: #000;
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-stop, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-stop {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.3);
}

.e-checkbox-wrapper.e-checkbox-disabled .e-label, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-label {
  color: rgba(255, 255, 255, 0.3);
}

.e-checkbox-wrapper.e-focus .e-ripple-container, .e-css.e-checkbox-wrapper.e-focus .e-ripple-container {
  background-color: rgba(255, 255, 255, 0.12);
}

.e-checkbox-wrapper.e-focus .e-ripple-container.e-ripple-check, .e-css.e-checkbox-wrapper.e-focus .e-ripple-container.e-ripple-check {
  background-color: rgba(0, 176, 255, 0.26);
}

.e-checkbox-wrapper.e-focus .e-frame, .e-css.e-checkbox-wrapper.e-focus .e-frame {
  outline: none 0 solid;
  outline-offset: 0;
}

.e-checkbox-wrapper.e-focus .e-frame.e-check, .e-css.e-checkbox-wrapper.e-focus .e-frame.e-check {
  outline: none 0 solid;
  outline-offset: 0;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
