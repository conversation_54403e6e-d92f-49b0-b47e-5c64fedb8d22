.e-checkbox-wrapper .e-check::before, .e-css.e-checkbox-wrapper .e-check::before {
  content: '\e21d';
}

.e-checkbox-wrapper .e-stop::before, .e-css.e-checkbox-wrapper .e-stop::before {
  content: '\e99f';
}

/*! checkbox layout */
.e-checkbox-wrapper, .e-css.e-checkbox-wrapper {
  cursor: pointer;
  display: inline-block;
  line-height: 1;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-checkbox-wrapper label, .e-css.e-checkbox-wrapper label {
  cursor: pointer;
  display: inline-block;
  line-height: 0;
  margin: 0;
  position: relative;
  white-space: nowrap;
}

.e-checkbox-wrapper:focus .e-frame, .e-css.e-checkbox-wrapper:focus .e-frame {
  box-shadow: 0 0 10px 0 rgba(36, 138, 255, 0.99);
}

.e-checkbox-wrapper .e-ripple-container, .e-css.e-checkbox-wrapper .e-ripple-container {
  border-radius: 50%;
  bottom: -9px;
  height: 36px;
  left: -9px;
  pointer-events: none;
  position: absolute;
  right: -9px;
  top: -9px;
  width: 36px;
  z-index: 1;
}

.e-checkbox-wrapper .e-label, .e-css.e-checkbox-wrapper .e-label {
  cursor: pointer;
  display: inline-block;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
  white-space: normal;
}

.e-checkbox-wrapper .e-checkbox, .e-css.e-checkbox-wrapper .e-checkbox {
  height: 1px;
  opacity: 0;
  position: absolute;
  width: 1px;
}

.e-checkbox-wrapper .e-checkbox + .e-label, .e-css.e-checkbox-wrapper .e-checkbox + .e-label {
  margin-right: 8px;
}

.e-checkbox-wrapper .e-frame, .e-css.e-checkbox-wrapper .e-frame {
  border: 1px solid;
  border-radius: 3px;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-family: 'e-icons';
  height: 20px;
  line-height: 18px;
  padding: 0;
  text-align: center;
  vertical-align: middle;
  width: 20px;
}

.e-checkbox-wrapper .e-frame + .e-label, .e-css.e-checkbox-wrapper .e-frame + .e-label {
  margin-left: 8px;
}

.e-checkbox-wrapper .e-frame + .e-ripple-container, .e-css.e-checkbox-wrapper .e-frame + .e-ripple-container {
  left: auto;
}

.e-checkbox-wrapper .e-check, .e-css.e-checkbox-wrapper .e-check {
  font-size: 12px;
}

.e-checkbox-wrapper .e-stop, .e-css.e-checkbox-wrapper .e-stop {
  font-size: 12px;
  line-height: 18px;
}

.e-checkbox-wrapper.e-checkbox-disabled, .e-css.e-checkbox-wrapper.e-checkbox-disabled {
  cursor: default;
  pointer-events: none;
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame {
  cursor: default;
}

.e-checkbox-wrapper.e-checkbox-disabled .e-label, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-label {
  cursor: default;
}

.e-checkbox-wrapper.e-rtl .e-ripple-container, .e-css.e-checkbox-wrapper.e-rtl .e-ripple-container {
  right: -9px;
}

.e-checkbox-wrapper.e-rtl .e-frame, .e-css.e-checkbox-wrapper.e-rtl .e-frame {
  margin: 0;
}

.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container {
  left: -9px;
  right: auto;
}

.e-checkbox-wrapper.e-rtl .e-label, .e-css.e-checkbox-wrapper.e-rtl .e-label {
  margin-left: 0;
  margin-right: 8px;
}

.e-checkbox-wrapper.e-rtl .e-label + .e-frame, .e-css.e-checkbox-wrapper.e-rtl .e-label + .e-frame {
  margin: 0;
}

.e-checkbox-wrapper.e-rtl .e-checkbox + .e-label, .e-css.e-checkbox-wrapper.e-rtl .e-checkbox + .e-label {
  margin-left: 8px;
  margin-right: 0;
}

.e-checkbox-wrapper.e-small .e-frame, .e-css.e-checkbox-wrapper.e-small .e-frame {
  height: 14px;
  line-height: 12px;
  width: 14px;
}

.e-checkbox-wrapper.e-small .e-check, .e-css.e-checkbox-wrapper.e-small .e-check {
  font-size: 8px;
}

.e-checkbox-wrapper.e-small .e-stop, .e-css.e-checkbox-wrapper.e-small .e-stop {
  font-size: 6px;
  line-height: 12px;
}

.e-checkbox-wrapper.e-small .e-label, .e-css.e-checkbox-wrapper.e-small .e-label {
  font-size: 14px;
  line-height: 14px;
}

.e-checkbox-wrapper.e-small .e-ripple-container, .e-css.e-checkbox-wrapper.e-small .e-ripple-container {
  bottom: -13px;
  height: 30px;
  left: -13px;
  right: -13px;
  top: -13px;
  width: 30px;
}

.e-small .e-checkbox-wrapper .e-frame, .e-small.e-checkbox-wrapper .e-frame, .e-small .e-css.e-checkbox-wrapper .e-frame, .e-small.e-css.e-checkbox-wrapper .e-frame {
  height: 14px;
  line-height: 12px;
  width: 14px;
}

.e-small .e-checkbox-wrapper .e-check, .e-small.e-checkbox-wrapper .e-check, .e-small .e-css.e-checkbox-wrapper .e-check, .e-small.e-css.e-checkbox-wrapper .e-check {
  font-size: 8px;
}

.e-small .e-checkbox-wrapper .e-stop, .e-small.e-checkbox-wrapper .e-stop, .e-small .e-css.e-checkbox-wrapper .e-stop, .e-small.e-css.e-checkbox-wrapper .e-stop {
  font-size: 6px;
  line-height: 12px;
}

.e-small .e-checkbox-wrapper .e-label, .e-small.e-checkbox-wrapper .e-label, .e-small .e-css.e-checkbox-wrapper .e-label, .e-small.e-css.e-checkbox-wrapper .e-label {
  font-size: 14px;
  line-height: 14px;
}

.e-small .e-checkbox-wrapper .e-ripple-container, .e-small.e-checkbox-wrapper .e-ripple-container, .e-small .e-css.e-checkbox-wrapper .e-ripple-container, .e-small.e-css.e-checkbox-wrapper .e-ripple-container {
  bottom: -13px;
  height: 30px;
  left: -13px;
  right: -13px;
  top: -13px;
  width: 30px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-frame, .e-bigger.e-small.e-checkbox-wrapper .e-frame, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-frame, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-frame {
  height: 18px;
  line-height: 16px;
  width: 18px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-check, .e-bigger.e-small.e-checkbox-wrapper .e-check, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-check, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-check {
  font-size: 10px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-stop, .e-bigger.e-small.e-checkbox-wrapper .e-stop, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-stop, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-stop {
  font-size: 12px;
  line-height: 16px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-label, .e-bigger.e-small.e-checkbox-wrapper .e-label, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-label, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-label {
  font-size: 15px;
  line-height: 18px;
}

.e-bigger.e-small .e-checkbox-wrapper .e-ripple-container, .e-bigger.e-small.e-checkbox-wrapper .e-ripple-container, .e-bigger.e-small .e-css.e-checkbox-wrapper .e-ripple-container, .e-bigger.e-small.e-css.e-checkbox-wrapper .e-ripple-container {
  bottom: -16px;
  height: 38px;
  left: -16px;
  right: -16px;
  top: -16px;
  width: 38px;
}

.e-bigger .e-checkbox-wrapper .e-frame, .e-bigger.e-checkbox-wrapper .e-frame, .e-bigger .e-css.e-checkbox-wrapper .e-frame, .e-bigger.e-css.e-checkbox-wrapper .e-frame {
  height: 22px;
  line-height: 20px;
  width: 22px;
}

.e-bigger .e-checkbox-wrapper .e-frame + .e-label, .e-bigger.e-checkbox-wrapper .e-frame + .e-label, .e-bigger .e-css.e-checkbox-wrapper .e-frame + .e-label, .e-bigger.e-css.e-checkbox-wrapper .e-frame + .e-label {
  font-size: 15px;
  line-height: 22px;
  margin-left: 10px;
}

.e-bigger .e-checkbox-wrapper .e-check, .e-bigger.e-checkbox-wrapper .e-check, .e-bigger .e-css.e-checkbox-wrapper .e-check, .e-bigger.e-css.e-checkbox-wrapper .e-check {
  font-size: 14px;
}

.e-bigger .e-checkbox-wrapper .e-stop, .e-bigger.e-checkbox-wrapper .e-stop, .e-bigger .e-css.e-checkbox-wrapper .e-stop, .e-bigger.e-css.e-checkbox-wrapper .e-stop {
  font-size: 14px;
  line-height: 20px;
}

.e-bigger .e-checkbox-wrapper .e-label, .e-bigger.e-checkbox-wrapper .e-label, .e-bigger .e-css.e-checkbox-wrapper .e-label, .e-bigger.e-css.e-checkbox-wrapper .e-label {
  font-size: 15px;
}

.e-bigger .e-checkbox-wrapper .e-ripple-container, .e-bigger.e-checkbox-wrapper .e-ripple-container, .e-bigger .e-css.e-checkbox-wrapper .e-ripple-container, .e-bigger.e-css.e-checkbox-wrapper .e-ripple-container {
  bottom: -15px;
  height: 38px;
  left: -15px;
  right: -15px;
  top: -15px;
  width: 38px;
}

.e-bigger .e-checkbox-wrapper.e-rtl .e-frame, .e-bigger.e-checkbox-wrapper.e-rtl .e-frame, .e-bigger .e-css.e-checkbox-wrapper.e-rtl .e-frame, .e-bigger.e-css.e-checkbox-wrapper.e-rtl .e-frame {
  margin: 0;
}

.e-bigger .e-checkbox-wrapper.e-rtl .e-frame + .e-label, .e-bigger.e-checkbox-wrapper.e-rtl .e-frame + .e-label, .e-bigger .e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-label, .e-bigger.e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-label {
  margin-left: 0;
  margin-right: 10px;
}

.e-bigger .e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-bigger.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-bigger .e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container, .e-bigger.e-css.e-checkbox-wrapper.e-rtl .e-frame + .e-ripple-container {
  right: auto;
}

.e-bigger .e-checkbox-wrapper.e-small .e-frame, .e-bigger.e-checkbox-wrapper.e-small .e-frame, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-frame, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-frame {
  height: 18px;
  line-height: 16px;
  width: 18px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-check, .e-bigger.e-checkbox-wrapper.e-small .e-check, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-check, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-check {
  font-size: 10px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-stop, .e-bigger.e-checkbox-wrapper.e-small .e-stop, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-stop, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-stop {
  font-size: 12px;
  line-height: 16px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-label, .e-bigger.e-checkbox-wrapper.e-small .e-label, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-label, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-label {
  font-size: 15px;
  line-height: 18px;
}

.e-bigger .e-checkbox-wrapper.e-small .e-ripple-container, .e-bigger.e-checkbox-wrapper.e-small .e-ripple-container, .e-bigger .e-css.e-checkbox-wrapper.e-small .e-ripple-container, .e-bigger.e-css.e-checkbox-wrapper.e-small .e-ripple-container {
  bottom: -16px;
  height: 38px;
  left: -16px;
  right: -16px;
  top: -16px;
  width: 38px;
}

/*! checkbox theme */
.e-checkbox-wrapper, .e-css.e-checkbox-wrapper {
  -webkit-tap-highlight-color: transparent;
}

.e-checkbox-wrapper .e-frame, .e-css.e-checkbox-wrapper .e-frame {
  background-color: #1a1a1a;
  border-color: #f0f0f0;
}

.e-checkbox-wrapper .e-frame.e-check, .e-css.e-checkbox-wrapper .e-frame.e-check {
  background-color: #1a1a1a;
  border-color: #f0f0f0;
  color: #f0f0f0;
}

.e-checkbox-wrapper .e-frame.e-stop, .e-css.e-checkbox-wrapper .e-frame.e-stop {
  background-color: #1a1a1a;
  border-color: #f0f0f0;
  color: #f0f0f0;
}

.e-checkbox-wrapper .e-ripple-element, .e-css.e-checkbox-wrapper .e-ripple-element {
  background: transparent;
}

.e-checkbox-wrapper .e-ripple-check .e-ripple-element, .e-css.e-checkbox-wrapper .e-ripple-check .e-ripple-element {
  background: transperant;
}

.e-checkbox-wrapper:active .e-ripple-element, .e-css.e-checkbox-wrapper:active .e-ripple-element {
  background: transperant;
}

.e-checkbox-wrapper:active .e-ripple-check .e-ripple-element, .e-css.e-checkbox-wrapper:active .e-ripple-check .e-ripple-element {
  background: transparent;
}

.e-checkbox-wrapper .e-label, .e-css.e-checkbox-wrapper .e-label {
  color: #f0f0f0;
}

.e-checkbox-wrapper .e-checkbox:focus + .e-frame, .e-css.e-checkbox-wrapper .e-checkbox:focus + .e-frame {
  background-color: #313131;
  border-color: #acacac;
  box-shadow: 0 0 10px 0 rgba(36, 138, 255, 0.99);
}

.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-check, .e-css.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-check {
  background-color: #313131;
  border-color: #fff;
  box-shadow: 0 0 10px 0 rgba(36, 138, 255, 0.99);
  color: #fff;
}

.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-stop, .e-css.e-checkbox-wrapper .e-checkbox:focus + .e-frame.e-stop {
  box-shadow: 0 0 10px 0 rgba(36, 138, 255, 0.99);
  color: #f0f0f0;
}

.e-checkbox-wrapper:hover .e-frame, .e-css.e-checkbox-wrapper:hover .e-frame {
  background-color: #313131;
  border-color: #acacac;
}

.e-checkbox-wrapper:hover .e-frame.e-check, .e-css.e-checkbox-wrapper:hover .e-frame.e-check {
  background-color: #313131;
  border-color: #fff;
  color: #fff;
}

.e-checkbox-wrapper:hover .e-frame.e-stop, .e-css.e-checkbox-wrapper:hover .e-frame.e-stop {
  color: #f0f0f0;
}

.e-checkbox-wrapper:hover .e-label, .e-css.e-checkbox-wrapper:hover .e-label {
  color: #fff;
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame {
  background-color: rgba(110, 110, 110, 0.35);
  border-color: rgba(240, 240, 240, 0.35);
  color: rgba(240, 240, 240, 0.35);
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-check, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-check {
  background-color: rgba(26, 26, 26, 0.35);
  border-color: rgba(240, 240, 240, 0.35);
  color: rgba(240, 240, 240, 0.35);
}

.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-stop, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame.e-stop {
  background-color: rgba(26, 26, 26, 0.35);
  border-color: rgba(240, 240, 240, 0.35);
  color: rgba(240, 240, 240, 0.35);
}

.e-checkbox-wrapper.e-checkbox-disabled .e-label, .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-label {
  color: rgba(240, 240, 240, 0.35);
}

.e-checkbox-wrapper.e-focus .e-ripple-container, .e-css.e-checkbox-wrapper.e-focus .e-ripple-container {
  background-color: transparent;
}

.e-checkbox-wrapper.e-focus .e-ripple-container.e-ripple-check, .e-css.e-checkbox-wrapper.e-focus .e-ripple-container.e-ripple-check {
  background-color: transparent;
}

.e-checkbox-wrapper.e-focus .e-frame, .e-css.e-checkbox-wrapper.e-focus .e-frame {
  outline: #1a1a1a 0 solid;
  outline-offset: 0;
}

.e-checkbox-wrapper.e-focus .e-frame.e-check, .e-css.e-checkbox-wrapper.e-focus .e-frame.e-check {
  outline: #1a1a1a 0 solid;
  outline-offset: 0;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
