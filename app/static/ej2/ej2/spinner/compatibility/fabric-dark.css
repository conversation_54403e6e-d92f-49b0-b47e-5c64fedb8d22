@keyframes material-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fabric-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

.e-spinner-pane {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 100%;
  -ms-flex-pack: center;
      justify-content: center;
  left: 0;
  position: absolute;
  text-align: center;
  top: 0;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
  width: 100%;
  z-index: 1000;
}

.e-spinner-pane::after {
  content: "Fabric";
  display: none;
}

.e-spinner-pane.e-spin-left .e-spinner-inner {
  -webkit-transform: translateX(0%) translateY(-50%);
  left: 0;
  padding-left: 10px;
  transform: translateX(0%) translateY(-50%);
}

.e-spinner-pane.e-spin-right .e-spinner-inner {
  -webkit-transform: translateX(-100%) translateY(-50%);
  left: 100%;
  padding-right: 10px;
  transform: translateX(-100%) translateY(-50%);
}

.e-spinner-pane.e-spin-center .e-spinner-inner {
  -webkit-transform: translateX(-50%) translateY(-50%);
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.e-spinner-pane.e-spin-hide {
  display: none;
}

.e-spinner-pane.e-spin-show {
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.e-spinner-pane .e-spinner-inner {
  -webkit-transform: translateX(-50%) translateY(-50%);
  left: 50%;
  margin: 0;
  position: absolute;
  text-align: center;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 1000;
}

.e-spinner-pane .e-spinner-inner .e-spin-label {
  font-family: "Segoe UI";
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
}

.e-spinner-pane .e-spinner-inner .e-spin-material {
  animation: material-spinner-rotate 1568.63ms linear infinite;
  display: block;
  margin: 0 auto;
}

.e-spinner-pane .e-spinner-inner .e-spin-material .e-path-circle {
  fill: none;
  stroke-linecap: square;
}

.e-spinner-pane .e-spinner-inner .e-spin-bootstrap4 {
  animation: material-spinner-rotate .75s linear infinite;
  display: block;
  margin: 0 auto;
}

.e-spinner-pane .e-spinner-inner .e-spin-bootstrap4 .e-path-circle {
  fill: none;
  stroke-linecap: square;
  stroke-width: 4;
}

.e-spinner-pane .e-spinner-inner .e-spin-fabric {
  animation: fabric-spinner-rotate 1.3s infinite cubic-bezier(0.53, 0.21, 0.29, 0.67);
  display: block;
  margin: 0 auto;
  overflow: visible;
}

.e-spinner-pane .e-spinner-inner .e-spin-fabric .e-path-arc, .e-spinner-pane .e-spinner-inner .e-spin-fabric .e-path-circle {
  fill: none;
  stroke-width: 1.5;
}

.e-spinner-pane .e-spinner-inner .e-spin-bootstrap {
  display: block;
  margin: 0 auto;
}

.e-spinner-pane .e-spinner-inner .e-spin-high-contrast {
  animation: fabric-spinner-rotate 1.3s infinite cubic-bezier(0.53, 0.21, 0.29, 0.67);
  display: block;
  margin: 0 auto;
  overflow: visible;
}

.e-spinner-pane .e-spinner-inner .e-spin-high-contrast .e-path-arc, .e-spinner-pane .e-spinner-inner .e-spin-high-contrast .e-path-circle {
  fill: none;
  stroke-width: 1.5;
}

.e-spinner-pane {
  background-color: transparent;
}

.e-spinner-pane.e-spin-overlay {
  background-color: rgba(248, 248, 248, 0.4);
}

.e-spinner-pane.e-spin-overlay .e-spinner-inner .e-spin-label {
  color: #201f1f;
}

.e-spinner-pane.e-spin-overlay .e-spinner-inner .e-spin-bootstrap {
  fill: #0074cc;
  stroke: #0074cc;
}

.e-spinner-pane .e-spinner-inner .e-spin-label {
  color: #0074cc;
}

.e-spinner-pane .e-spinner-inner .e-spin-fabric .e-path-circle {
  stroke: #addcff;
}

.e-spinner-pane .e-spinner-inner .e-spin-fabric .e-path-arc {
  stroke: #0074cc;
}

.e-spinner-pane .e-spinner-inner .e-spin-high-contrast .e-path-circle {
  stroke: #addcff;
}

.e-spinner-pane .e-spinner-inner .e-spin-high-contrast .e-path-arc {
  stroke: #0074cc;
}

.e-spinner-pane .e-spinner-inner .e-spin-material {
  stroke: #0074cc;
}

.e-spinner-pane .e-spinner-inner .e-spin-bootstrap4 {
  stroke: #0074cc;
}

.e-spinner-pane .e-spinner-inner .e-spin-bootstrap {
  fill: #0074cc;
  stroke: #0074cc;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
