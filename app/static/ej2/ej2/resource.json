{"grid": {"classname": "Grid", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs", "ej2-navigations/ContextMenu,Toolbar", "ej2-calendars/DatePicker", "ej2-dropdowns/AutoComplete,DropDownList", "ej2-popups", "ej2-buttons", "ej2-excel-export", "ej2-pdf-export", "ej2-file-utils", "ej2-compression", "ej2-splitbuttons"], "package": "ej2-grids"}, "dialog": {"classname": "Dialog", "dependencies": ["ej2-base", "ej2-popups/Popup", "ej2-buttons/Button"], "package": "ej2-popups"}, "toast": {"classname": "Toast", "dependencies": ["ej2-base", "ej2-buttons/Button"], "package": "ej2-notifications"}, "drop-down-list": {"classname": "DropDownList", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs", "ej2-buttons/Button", "ej2-popups/Popup"], "package": "ej2-dropdowns"}, "combo-box": {"classname": "ComboBox", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs", "ej2-buttons/Button", "ej2-popups/Popup", "ej2-dropdowns/DropDownList"], "package": "ej2-dropdowns"}, "auto-complete": {"classname": "AutoComplete", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs", "ej2-buttons/Button", "ej2-popups/Popup", "ej2-dropdowns/DropDownList,ComboBox"], "package": "ej2-dropdowns"}, "multi-select": {"classname": "MultiSelect", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs", "ej2-buttons/Button", "ej2-popups/Popup"], "package": "ej2-dropdowns"}, "linear-gauge": {"classname": "LinearGauge", "dependencies": ["ej2-base"], "package": "ej2-lineargauge"}, "button": {"classname": "<PERSON><PERSON>", "dependencies": ["ej2-base"], "package": "ej2-buttons"}, "check-box": {"classname": "CheckBox", "dependencies": ["ej2-base"], "package": "ej2-buttons"}, "radio-button": {"classname": "RadioButton", "dependencies": ["ej2-base"], "package": "ej2-buttons"}, "maskedtextbox": {"classname": "MaskedTextBox", "dependencies": ["ej2-base", "ej2-inputs/MaskedTextBox/base", "ej2-inputs/Input"], "package": "ej2-inputs"}, "numerictextbox": {"classname": "NumericTextBox", "dependencies": ["ej2-base", "ej2-inputs/Input"], "package": "ej2-inputs"}, "slider": {"classname": "Slide<PERSON>", "dependencies": ["ej2-base", "ej2-popups/Tooltip"], "package": "ej2-inputs"}, "uploader": {"classname": "Uploader", "dependencies": ["ej2-base"], "package": "ej2-inputs"}, "maps": {"classname": "Maps", "dependencies": ["ej2-base", "ej2-data", "ej2-svg-base"], "package": "ej2-maps"}, "calendar": {"classname": "Calendar", "dependencies": ["ej2-base", "ej2-buttons/Button"], "package": "ej2-calendars"}, "datepicker": {"classname": "DatePicker", "dependencies": ["ej2-base", "ej2-popups/Popup", "ej2-inputs/Input", "ej2-calendars/Calendar"], "package": "ej2-calendars"}, "daterangepicker": {"classname": "DateRangePicker", "dependencies": ["ej2-base", "ej2-popups/Popup", "ej2-buttons/Button", "ej2-inputs/Input", "ej2-lists", "ej2-calendars/Calendar"], "package": "ej2-calendars"}, "datetimepicker": {"classname": "DateTimePicker", "dependencies": ["ej2-base", "ej2-popups/Popup", "ej2-inputs/Input", "ej2-lists", "ej2-calendars/DatePicker,TimePicker,DateTimePicker"], "package": "ej2-calendars"}, "timepicker": {"classname": "TimePicker", "dependencies": ["ej2-base", "ej2-popups/Popup", "ej2-inputs/Input", "ej2-lists", "ej2-calendars/TimePicker"], "package": "ej2-calendars"}, "treeview": {"classname": "TreeView", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs/Input", "ej2-popups", "ej2-buttons"], "package": "ej2-navigations"}, "pager": {"classname": "Pager", "dependencies": ["ej2-base"], "package": "ej2-grids"}, "drop-down-button": {"classname": "DropDownButton", "dependencies": ["ej2-base", "ej2-popups/Popup", "ej2-buttons/Button"], "package": "ej2-splitbuttons"}, "split-button": {"classname": "SplitButton", "dependencies": ["ej2-base", "ej2-popups/Popup", "ej2-buttons/Button"], "package": "ej2-splitbuttons"}, "chart": {"classname": "Chart", "dependencies": ["ej2-base", "ej2-svg-base", "ej2-data", "ej2-pdf-export", "ej2-file-utils", "ej2-compression"], "package": "ej2-charts"}, "accumulation-chart": {"classname": "AccumulationChart", "dependencies": ["ej2-base", "ej2-svg-base", "ej2-data", "ej2-pdf-export", "ej2-file-utils", "ej2-compression"], "package": "ej2-charts"}, "circular-gauge": {"classname": "CircularGauge", "dependencies": ["ej2-base"], "package": "ej2-circulargauge"}, "accordion": {"classname": "Accordion", "dependencies": ["ej2-base"], "package": "ej2-navigations"}, "toolbar": {"classname": "<PERSON><PERSON><PERSON>", "dependencies": ["ej2-base", "ej2-buttons/Button", "ej2-popups/Popup", "ej2-navigations/HScroll", "ej2-navigations/VScroll"], "package": "ej2-navigations"}, "tab": {"classname": "Tab", "dependencies": ["ej2-base", "ej2-buttons/Button", "ej2-popups/Popup", "ej2-navigations/HScroll", "ej2-navigations/VScroll", "ej2-navigations/Toolbar"], "package": "ej2-navigations"}, "context-menu": {"classname": "ContextMenu", "dependencies": ["ej2-base", "ej2-lists", "ej2-popups/Popup"], "package": "ej2-navigations"}, "menu": {"classname": "<PERSON><PERSON>", "dependencies": ["ej2-base", "ej2-lists", "ej2-popups/Popup"], "package": "ej2-navigations"}, "schedule": {"classname": "Schedule", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs/FormValidator, Input, NumericTextBox", "ej2-buttons/Button, CheckBox", "ej2-lists", "ej2-popups", "ej2-calendars/Calendar, DatePicker, DateTimePicker", "ej2-dropdowns/DropDownList", "ej2-navigations/Toolbar"], "package": "ej2-schedule"}, "recurrence-editor": {"classname": "RecurrenceEditor", "dependencies": ["ej2-base", "ej2-inputs/NumericTextBox", "ej2-buttons/Button, RadioButton", "ej2-calendars/DatePicker", "ej2-dropdowns/DropDownList"], "package": "ej2-schedule"}, "sidebar": {"classname": "Sidebar", "dependencies": ["ej2-base"], "package": "ej2-navigations"}, "range-navigator": {"classname": "RangeNavigator", "dependencies": ["ej2-base", "ej2-svg-base", "ej2-data", "ej2-pdf-export", "ej2-file-utils", "ej2-compression", "ej2-navigations", "ej2-calendars"], "package": "ej2-charts"}, "treemap": {"classname": "TreeMap", "dependencies": ["ej2-base", "ej2-svg-base"], "package": "ej2-treemap"}, "smithchart": {"classname": "Smithchart", "dependencies": ["ej2-base", "ej2-svg-base", "ej2-data", "ej2-pdf-export", "ej2-file-utils", "ej2-compression"], "package": "ej2-charts"}, "switch": {"classname": "Switch", "dependencies": ["ej2-base"], "package": "ej2-buttons"}, "color-picker": {"classname": "ColorPicker", "dependencies": ["ej2-base", "ej2-buttons/Button", "ej2-inputs/Slider", "ej2-inputs/Input", "ej2-splitbuttons/SplitButton", "ej2-popups/Tooltip"], "package": "ej2-inputs"}, "diagram": {"classname": "Diagram", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs", "ej2-navigations/ContextMenu,Accordion", "ej2-popups", "ej2-buttons"], "package": "ej2-diagrams"}, "symbol-palette": {"classname": "SymbolPalette", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs", "ej2-navigations/Accordion"], "package": "ej2-diagrams"}, "overview": {"classname": "Overview", "dependencies": ["ej2-base"], "package": "ej2-diagrams"}, "document-editor": {"classname": "DocumentEditor", "dependencies": ["ej2-base", "ej2-inputs", "ej2-navigations/ContextMenu,Tab", "ej2-dropdowns/DropDownList", "ej2-popups", "ej2-buttons", "ej2-file-utils", "ej2-compression", "ej2-splitbuttons", "ej2-office-chart"], "package": "ej2-documenteditor"}, "list-view": {"classname": "ListView", "dependencies": ["ej2-base", "ej2-buttons/CheckBox", "ej2-data"], "package": "ej2-lists"}, "pivotview": {"classname": "PivotView", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs", "ej2-lists", "ej2-navigations/TreeView,ContextMenu,Tab,Menu,<PERSON><PERSON><PERSON>", "ej2-calendars/Calendar, DatePicker, DateTimePicker", "ej2-dropdowns/DropDownList", "ej2-popups", "ej2-buttons", "ej2-excel-export", "ej2-pdf-export", "ej2-file-utils", "ej2-compression", "ej2-grids", "ej2-charts"], "package": "ej2-pivotview"}, "pivotfieldlist": {"classname": "PivotFieldList", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs", "ej2-lists", "ej2-navigations/TreeView,ContextMenu,Tab,Menu,<PERSON><PERSON><PERSON>", "ej2-calendars/Calendar, DatePicker, DateTimePicker", "ej2-dropdowns/DropDownList", "ej2-popups", "ej2-buttons"], "package": "ej2-pivotview"}, "rich-text-editor": {"classname": "RichTextEditor", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs/ColorPicker,NumericTextBox,Uploader", "ej2-buttons/Button,CheckBox", "ej2-popups/Dialog,Popup", "ej2-navigations/Toolbar", "ej2-splitbuttons/DropDownButton"], "package": "ej2-richtexteditor"}, "heatmap": {"classname": "HeatMap", "dependencies": ["ej2-base", "ej2-data", "ej2-svg-base"], "package": "ej2-heatmap"}, "sparkline": {"classname": "Sparkline", "dependencies": ["ej2-base", "ej2-svg-base"], "package": "ej2-charts"}, "tooltip": {"classname": "<PERSON><PERSON><PERSON>", "dependencies": ["ej2-base", "ej2-popups/Popup"], "package": "ej2-popups"}, "textbox": {"classname": "TextBox", "dependencies": ["ej2-base", "ej2-inputs/Input"], "package": "ej2-inputs"}, "avatar": {"package": "ej2-layouts"}, "badge": {"package": "ej2-notifications"}, "card": {"package": "ej2-cards"}, "progress-button": {"classname": "ProgressButton", "dependencies": ["ej2-base", "ej2-popups/Spinner", "ej2-buttons/Button"], "package": "ej2-splitbuttons"}, "h-scroll": {"package": "ej2-navigations"}, "v-scroll": {"package": "ej2-navigations"}, "document-editor-container": {"classname": "DocumentEditorContainer", "dependencies": ["ej2-base", "ej2-navigations/Toolbar"], "package": "ej2-documenteditor"}, "chips": {"classname": "ChipList", "dependencies": ["ej2-base"], "package": "ej2-buttons"}, "treegrid": {"classname": "Tree<PERSON><PERSON>", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs", "ej2-navigations/ContextMenu,Toolbar", "ej2-calendars/DatePicker", "ej2-dropdowns/AutoComplete,DropDownList", "ej2-popups", "ej2-buttons", "ej2-excel-export", "ej2-pdf-export", "ej2-file-utils", "ej2-compression", "ej2-splitbuttons", "ej2-grids"], "package": "ej2-treegrid"}, "pdfviewer": {"classname": "PdfViewer", "dependencies": ["ej2-base", "ej2-popups", "ej2-buttons", "ej2-inputs", "ej2-navigations", "ej2-dropdowns", "ej2-lists", "ej2-splitbuttons"], "package": "ej2-pdfviewer"}, "splitter": {"classname": "Splitter", "dependencies": ["ej2-base"], "package": "ej2-layouts"}, "inplaceeditor": {"classname": "InPlaceEditor", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs/Input,ColorPicker,MaskedTextBox,NumericTextBox,Slider,TextBox,FormValidator", "ej2-buttons/Button", "ej2-calendars/DatePicker,DateRangePicker,DateTimePicker,TimePicker", "ej2-dropdowns", "ej2-popups/Popup,Tooltip", "ej2-richtexteditor"], "package": "ej2-inplace-editor"}, "query-builder": {"classname": "QueryBuilder", "dependencies": ["ej2-base", "ej2-buttons/Button", "ej2-buttons/RadioButton", "ej2-splitbuttons/ButtonGroup", "ej2-splitbuttons/DropDownButton", "ej2-data", "ej2-inputs/NumericTextBox", "ej2-inputs/TextBox", "ej2-dropdowns/DropDownList", "ej2-calendars/DatePicker"], "package": "ej2-querybuilder"}, "inplace-editor": {"classname": "InPlaceEditor", "dependencies": ["ej2-base", "ej2-data", "ej2-lists", "ej2-inputs/Input,ColorPicker,MaskedTextBox,NumericTextBox,Slider,TextBox,FormValidator", "ej2-buttons/Button", "ej2-calendars/DatePicker,DateRangePicker,DateTimePicker,TimePicker", "ej2-dropdowns", "ej2-popups/Popup,Tooltip", "ej2-richtexteditor"], "package": "ej2-inplace-editor"}, "dashboardlayout": {"classname": "DashboardLayout", "dependencies": ["ej2-base"], "package": "ej2-layouts"}, "stock-chart": {"classname": "<PERSON><PERSON><PERSON>", "dependencies": ["ej2-base", "ej2-svg-base", "ej2-data", "ej2-pdf-export", "ej2-file-utils", "ej2-compression", "ej2-navigations", "ej2-calendars", "ej2-buttons"], "package": "ej2-charts"}, "icons": {"classname": "e-icons", "dependencies": [], "package": "ej2-icons"}, "gantt": {"classname": "<PERSON><PERSON><PERSON>", "dependencies": ["ej2-base", "ej2-data", "ej2-grids", "ej2-treegrid", "ej2-richtexteditor", "ej2-calendars", "ej2-buttons", "ej2-dropdowns", "ej2-navigations", "ej2-inputs", "ej2-popups", "ej2-layouts"], "package": "ej2-gantt"}, "file-manager": {"classname": "FileManager", "dependencies": ["ej2-base", "ej2-data", "ej2-inputs", "ej2-popups", "ej2-buttons", "ej2-splitbuttons", "ej2-navigations", "ej2-layouts", "ej2-grids"], "package": "ej2-filemanager"}, "sortable": {"classname": "Sortable", "dependencies": ["ej2-base"], "package": "ej2-lists"}, "list-box": {"classname": "ListBox", "dependencies": ["ej2-base", "ej2-data", "ej2-lists"], "package": "ej2-dropdowns"}, "dashboard-layout": {"classname": "DashboardLayout", "dependencies": ["ej2-base"], "package": "ej2-layouts"}, "barcode": {"classname": "Barcode", "dependencies": ["ej2-base", "ej2-data"], "package": "ej2-barcode-generator"}, "datamatrix": {"classname": "DataMatrixGenerator", "dependencies": ["ej2-base", "ej2-data"], "package": "ej2-barcode-generator"}, "qrcode": {"classname": "QRCodeGenerator", "dependencies": ["ej2-base", "ej2-data"], "package": "ej2-barcode-generator"}, "drawing": {"classname": "Drawing", "dependencies": ["ej2-base", "ej2-data"], "package": "ej2-drawings"}}