/*! component's default definitions and variables */
/*! component's theme wise override material-definitions and variables */
/*! card layout */
.e-bigger .e-card,
.e-bigger.e-card {
  line-height: 36px;
  min-height: 36px;
}

.e-bigger .e-card > .e-card-header-title,
.e-bigger.e-card > .e-card-header-title {
  line-height: 12px;
  padding: 12px;
}

.e-bigger .e-card > .e-card-header-title + :not(.e-card-header-title),
.e-bigger.e-card > .e-card-header-title + :not(.e-card-header-title) {
  margin-top: 0;
  padding-top: 0;
}

.e-bigger .e-card > .e-card-title,
.e-bigger.e-card > .e-card-title {
  line-height: 13px;
  padding: 18px 12px 12px;
}

.e-bigger .e-card > .e-card-title + :not(.e-card-title),
.e-bigger.e-card > .e-card-title + :not(.e-card-title) {
  margin-top: 0;
  padding-top: 0;
}

.e-bigger .e-card.e-card-horizontal .e-card-image,
.e-bigger .e-card.e-card-horizontal img,
.e-bigger .e-card .e-card-horizontal .e-card-image,
.e-bigger .e-card .e-card-horizontal img,
.e-bigger.e-card.e-card-horizontal .e-card-image,
.e-bigger.e-card.e-card-horizontal img,
.e-bigger.e-card .e-card-horizontal .e-card-image,
.e-bigger.e-card .e-card-horizontal img {
  margin: 2px;
}

.e-bigger .e-card .e-card-header,
.e-bigger.e-card .e-card-header {
  min-height: 22.5px;
  padding: 12px;
}

.e-bigger .e-card .e-card-header .e-card-content,
.e-bigger.e-card .e-card-header .e-card-content {
  padding-left: 0;
  padding-right: 0;
}

.e-bigger .e-card .e-card-header .e-card-header-caption,
.e-bigger.e-card .e-card-header .e-card-header-caption {
  padding: 0 0 0 12px;
}

.e-bigger .e-card .e-card-header .e-card-header-caption:first-child,
.e-bigger.e-card .e-card-header .e-card-header-caption:first-child {
  padding: 0;
}

.e-bigger .e-card .e-card-header .e-card-header-caption .e-card-header-title,
.e-bigger.e-card .e-card-header .e-card-header-caption .e-card-header-title {
  font-size: 15px;
  line-height: 13.5px;
}

.e-bigger .e-card .e-card-header .e-card-header-caption .e-card-sub-title,
.e-bigger.e-card .e-card-header .e-card-header-caption .e-card-sub-title {
  font-size: 14px;
  line-height: 10.5px;
  padding: 0 0 6px;
}

.e-bigger .e-card .e-card-header .e-card-header-image,
.e-bigger.e-card .e-card-header .e-card-header-image {
  background-size: cover;
  height: 30px;
  width: 30px;
}

.e-bigger .e-card .e-card-image,
.e-bigger.e-card .e-card-image {
  min-height: 112.5px;
}

.e-bigger .e-card .e-card-image .e-card-title,
.e-bigger.e-card .e-card-image .e-card-title {
  font-size: 24px;
  line-height: 18px;
  min-height: 30px;
  padding: 6px;
}

.e-bigger .e-card .e-card-actions,
.e-bigger.e-card .e-card-actions {
  padding: 6px;
}

.e-bigger .e-card .e-card-actions.e-card-vertical,
.e-bigger.e-card .e-card-actions.e-card-vertical {
  padding: 0 6px;
}

.e-bigger .e-card .e-card-actions.e-card-vertical .e-card-btn,
.e-bigger .e-card .e-card-actions.e-card-vertical a,
.e-bigger.e-card .e-card-actions.e-card-vertical .e-card-btn,
.e-bigger.e-card .e-card-actions.e-card-vertical a {
  margin: 0 0 6px 0;
}

.e-bigger .e-card .e-card-actions.e-card-vertical .e-card-btn:last-child,
.e-bigger .e-card .e-card-actions.e-card-vertical a:last-child,
.e-bigger.e-card .e-card-actions.e-card-vertical .e-card-btn:last-child,
.e-bigger.e-card .e-card-actions.e-card-vertical a:last-child {
  margin-bottom: 0;
}

.e-bigger .e-card .e-card-actions .e-card-btn span,
.e-bigger .e-card .e-card-actions .e-card-btn span.e-icons,
.e-bigger.e-card .e-card-actions .e-card-btn span,
.e-bigger.e-card .e-card-actions .e-card-btn span.e-icons {
  height: 24px;
  width: 24px;
}

.e-bigger .e-card .e-card-actions .e-card-btn,
.e-bigger .e-card .e-card-actions a,
.e-bigger.e-card .e-card-actions .e-card-btn,
.e-bigger.e-card .e-card-actions a {
  line-height: 27px;
  margin: 0 0 0 6px;
  min-height: 27px;
  padding: 0 6px;
}

.e-bigger .e-card .e-card-actions .e-card-btn:first-child,
.e-bigger .e-card .e-card-actions a:first-child,
.e-bigger.e-card .e-card-actions .e-card-btn:first-child,
.e-bigger.e-card .e-card-actions a:first-child {
  margin-left: 0;
}

.e-bigger .e-card .e-card-content,
.e-bigger.e-card .e-card-content {
  padding: 12px;
}

.e-bigger .e-card .e-card-content + :not(.e-card-content),
.e-bigger .e-card .e-card-content + .e-card-actions.e-card-vertical,
.e-bigger.e-card .e-card-content + :not(.e-card-content),
.e-bigger.e-card .e-card-content + .e-card-actions.e-card-vertical {
  margin-top: 0;
  padding-top: 0;
}

.e-card {
  border-radius: 0;
  box-sizing: border-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 15px;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 16px;
  min-height: 48px;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: middle;
  width: 100%;
}

.e-card > * {
  -ms-flex-pack: center;
      justify-content: center;
}

.e-card > .e-card-header-title {
  box-sizing: border-box;
  font-size: 15px;
  line-height: 16px;
  padding: 16px;
}

.e-card > .e-card-header-title + :not(.e-card-header-title) {
  margin-top: 0;
  padding-top: 0;
}

.e-card > .e-card-title {
  font-size: 18px;
  line-height: 12px;
  padding: 8px;
}

.e-card > .e-card-title + :not(.e-card-title) {
  margin-top: 0;
  padding-top: 0;
}

.e-card > .e-card-header-title,
.e-card > .e-card-title {
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-card .e-card-header-caption {
  line-height: normal;
}

.e-card .e-card-header-caption .e-card-header-title {
  font-size: 15px;
}

.e-card .e-card-header-caption .e-card-sub-title {
  font-size: 14px;
}

.e-card .e-card-stacked {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-pack: center;
      justify-content: center;
  overflow: hidden;
}

.e-card.e-card-horizontal,
.e-card .e-card-horizontal {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-pack: center;
      justify-content: center;
}

.e-card.e-card-horizontal .e-card-image,
.e-card.e-card-horizontal img,
.e-card .e-card-horizontal .e-card-image,
.e-card .e-card-horizontal img {
  margin: 2px;
}

.e-card .e-card-horizontal {
  line-height: normal;
  padding: 2px;
}

.e-card.e-card-horizontal > * {
  -ms-flex: 1;
      flex: 1;
}

.e-card.e-card-horizontal .e-card-stacked {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 1;
      flex: 1;
  -ms-flex-direction: column;
      flex-direction: column;
  overflow: hidden;
}

.e-card.e-card-horizontal .e-card-stacked > :first-child {
  -ms-flex-positive: 1;
      flex-grow: 1;
}

.e-card .e-card-separator {
  display: block;
}

.e-card .e-card-corner {
  border-radius: 50%;
}

.e-card .e-card-header {
  box-sizing: border-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: normal;
  min-height: 30px;
  padding: 16px 0 16px 16px;
  width: inherit;
}

.e-card .e-card-header .e-card-content {
  padding-left: 0;
  padding-right: 0;
}

.e-card .e-card-header .e-card-actions {
  -ms-flex-pack: start;
      justify-content: flex-start;
}

.e-card .e-card-header .e-card-header-image {
  -ms-flex-item-align: center;
      align-self: center;
  display: -ms-flexbox;
  display: flex;
}

.e-card .e-card-header .e-card-header-caption {
  -ms-flex-item-align: center;
      align-self: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 1;
      flex: 1;
  -ms-flex-direction: column;
      flex-direction: column;
  overflow: hidden;
  padding: 0 0 0 8px;
}

.e-card .e-card-header .e-card-header-caption:first-child {
  padding: 0;
}

.e-card .e-card-header .e-card-header-caption .e-card-header-title,
.e-card .e-card-header .e-card-header-caption .e-card-sub-title {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-card .e-card-header .e-card-header-caption .e-card-header-title {
  font-size: 13px;
  font-weight: 400;
  line-height: 18px;
}

.e-card .e-card-header .e-card-header-caption .e-card-sub-title {
  font-size: 12px;
  font-weight: 400;
  line-height: 14px;
  padding: 0 0 8px;
}

.e-card .e-card-header .e-card-header-image {
  background-repeat: no-repeat;
  background-size: cover;
  height: 40px;
  width: 40px;
}

.e-card .e-card-header .e-card-corner {
  border-radius: 50%;
}

.e-card .e-card-image {
  background-size: cover;
  min-height: 150px;
  position: relative;
  width: 100%;
}

.e-card .e-card-image .e-card-title {
  bottom: 0;
  box-sizing: border-box;
  font-size: 24px;
  line-height: 22px;
  min-height: 40px;
  overflow: hidden;
  padding: 16px;
  position: absolute;
  text-overflow: ellipsis;
  width: inherit;
}

.e-card .e-card-image .e-card-title.e-card-top-left, .e-card .e-card-image .e-card-title.e-card-top-right {
  bottom: auto;
  top: 0;
}

.e-card .e-card-image .e-card-title.e-card-top-right {
  text-align: right;
}

.e-card .e-card-image .e-card-title.e-card-bottom-right {
  text-align: right;
}

.e-card .e-card-actions {
  box-sizing: border-box;
  display: inline-block;
  -ms-flex-pack: start;
      justify-content: flex-start;
  padding: 8px;
}

.e-card .e-card-actions.e-card-vertical {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  padding: 0 8px;
}

.e-card .e-card-actions.e-card-vertical .e-card-btn,
.e-card .e-card-actions.e-card-vertical a {
  -ms-flex-item-align: initial;
      -ms-grid-row-align: initial;
      align-self: initial;
  box-sizing: border-box;
  display: inline-block;
  -ms-flex-pack: center;
      justify-content: center;
  margin: 0 0 8px 0;
  text-align: center;
  width: 100%;
}

.e-card .e-card-actions.e-card-vertical .e-card-btn:last-child,
.e-card .e-card-actions.e-card-vertical a:last-child {
  margin-bottom: 0;
}

.e-card .e-card-actions button,
.e-card .e-card-actions a {
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-item-align: center;
      align-self: center;
  cursor: pointer;
  display: -ms-inline-flexbox;
  display: inline-flex;
  overflow: hidden;
  vertical-align: middle;
  white-space: nowrap;
}

.e-card .e-card-actions .e-card-btn span,
.e-card .e-card-actions .e-card-btn span.e-icons {
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  height: 24px;
  width: 24px;
}

.e-card .e-card-actions .e-card-btn span::before,
.e-card .e-card-actions .e-card-btn span.e-icons::before {
  -ms-flex-item-align: center;
      align-self: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  width: inherit;
}

.e-card .e-card-actions .e-card-btn > * {
  overflow: hidden;
  text-overflow: ellipsis;
  width: inherit;
}

.e-card .e-card-actions .e-card-btn,
.e-card .e-card-actions a {
  border-radius: 2px;
  font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  line-height: 36px;
  margin: 0 0 0 8px;
  min-height: 36px;
  padding: 0 6px;
  text-decoration: none;
  text-transform: none;
}

.e-card .e-card-actions .e-card-btn:first-child,
.e-card .e-card-actions a:first-child {
  margin-left: 0;
}

.e-card .e-card-content {
  font-size: 14px;
  line-height: normal;
  padding: 16px;
}

.e-card .e-card-content + :not(.e-card-content),
.e-card .e-card-content + .e-card-actions.e-card-vertical {
  margin-top: 0;
  padding-top: 0;
}

.e-card .e-card-content,
.e-card .e-card-content p {
  line-height: normal;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*! card theme */
.e-card {
  -webkit-tap-highlight-color: transparent;
  background-color: #6e6e6e;
  border: 1px solid #1a1a1a;
  box-shadow: rgba(0, 0, 0, 0.4);
  color: #fff;
  outline: none;
}

.e-card:hover {
  background-color: #6e6e6e;
  border-color: #1a1a1a;
}

.e-card:focus {
  background-color: #6e6e6e;
  border-color: #1a1a1a;
}

.e-card:active {
  background-color: #6e6e6e;
  border-color: #1a1a1a;
}

.e-card .e-card-separator {
  border-bottom: 1px solid #1a1a1a;
}

.e-card .e-card-header-caption .e-card-header-title {
  color: #fff;
}

.e-card .e-card-header-caption .e-card-sub-title {
  color: #fff;
}

.e-card .e-card-image .e-card-title {
  background-color: #6e6e6e;
  color: #fff;
}

.e-card .e-card-actions .e-card-btn span,
.e-card .e-card-actions .e-card-btn span.e-icons {
  color: #fff;
}

.e-card .e-card-actions .e-card-btn,
.e-card .e-card-actions a {
  background-color: #6e6e6e;
  border: 0;
  color: #fff;
  outline: 0;
}

.e-card .e-card-actions .e-card-btn:hover,
.e-card .e-card-actions a:hover {
  background-color: #6e6e6e;
  border: 0;
  color: #fff;
}

.e-card .e-card-actions .e-card-btn:focus,
.e-card .e-card-actions a:focus {
  background-color: #6e6e6e;
  border: 0;
  color: #fff;
}

.e-card .e-card-actions .e-card-btn:active,
.e-card .e-card-actions a:active {
  background-color: #6e6e6e;
  border: 0;
  color: #fff;
}

.e-card .e-card-content {
  color: #fff;
}
