/*! button layout */
.e-btn,
.e-css.e-btn {
  -webkit-font-smoothing: antialiased;
  border: 1px solid;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 600;
  -ms-flex-pack: center;
      justify-content: center;
  line-height: 1.8572em;
  outline: none;
  padding: 2px 20px;
  text-align: center;
  text-decoration: none;
  text-transform: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
  white-space: nowrap;
}

.e-btn:disabled,
.e-css.e-btn:disabled {
  cursor: default;
}

.e-btn:hover, .e-btn:focus,
.e-css.e-btn:hover,
.e-css.e-btn:focus {
  text-decoration: none;
}

.e-btn::-moz-focus-inner,
.e-css.e-btn::-moz-focus-inner {
  border: 0;
  padding: 0;
}

.e-btn .e-btn-icon,
.e-css.e-btn .e-btn-icon {
  display: inline-block;
  font-size: 14px;
  margin-top: -3px;
  vertical-align: middle;
  width: 1em;
}

.e-btn .e-btn-icon.e-icon-left,
.e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.5714em;
  width: 1.929em;
}

.e-btn .e-btn-icon.e-icon-right,
.e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.5714em;
  width: 1.929em;
}

.e-btn .e-btn-icon.e-icon-top,
.e-css.e-btn .e-btn-icon.e-icon-top {
  display: block;
  margin-top: 0;
  padding-bottom: 8px;
  width: auto;
}

.e-btn .e-btn-icon.e-icon-bottom,
.e-css.e-btn .e-btn-icon.e-icon-bottom {
  display: block;
  margin-top: 0;
  padding-top: 8px;
  width: auto;
}

.e-btn.e-icon-btn,
.e-css.e-btn.e-icon-btn {
  padding: 2px 8px;
}

.e-btn.e-top-icon-btn, .e-btn.e-bottom-icon-btn,
.e-css.e-btn.e-top-icon-btn,
.e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 8px 20px;
}

.e-btn.e-round,
.e-css.e-btn.e-round {
  border-radius: 50%;
  height: 36px;
  line-height: 1;
  padding: 0;
  width: 36px;
}

.e-btn.e-round .e-btn-icon,
.e-css.e-btn.e-round .e-btn-icon {
  font-size: 16px;
  line-height: 26px;
  margin-top: 0;
  width: auto;
}

.e-btn.e-rtl .e-icon-right,
.e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.5714em;
  margin-right: 0;
}

.e-btn.e-rtl .e-icon-left,
.e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.5714em;
}

.e-btn.e-flat,
.e-css.e-btn.e-flat {
  border: 1px solid;
}

.e-btn.e-small,
.e-css.e-btn.e-small {
  font-size: 12px;
  line-height: 1.5em;
  padding: 3px 16px;
}

.e-btn.e-small .e-btn-icon,
.e-css.e-btn.e-small .e-btn-icon {
  font-size: 11px;
  width: 0.91em;
}

.e-btn.e-small .e-btn-icon.e-icon-left,
.e-css.e-btn.e-small .e-btn-icon.e-icon-left {
  margin-left: -0.7273em;
  width: 2.182em;
}

.e-btn.e-small .e-btn-icon.e-icon-right,
.e-css.e-btn.e-small .e-btn-icon.e-icon-right {
  margin-right: -0.7273em;
  width: 2.182em;
}

.e-btn.e-small .e-btn-icon.e-icon-top,
.e-css.e-btn.e-small .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-btn.e-small .e-btn-icon.e-icon-bottom,
.e-css.e-btn.e-small .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-btn.e-small.e-icon-btn,
.e-css.e-btn.e-small.e-icon-btn {
  padding: 3px 6px;
}

.e-btn.e-small.e-top-icon-btn, .e-btn.e-small.e-bottom-icon-btn,
.e-css.e-btn.e-small.e-top-icon-btn,
.e-css.e-btn.e-small.e-bottom-icon-btn {
  line-height: 1;
  padding: 8px 20px;
}

.e-btn.e-small.e-round,
.e-css.e-btn.e-small.e-round {
  height: 2.5em;
  line-height: 1;
  padding: 0;
  width: 2.5em;
}

.e-btn.e-small.e-round .e-btn-icon,
.e-css.e-btn.e-small.e-round .e-btn-icon {
  font-size: 14px;
  line-height: 16px;
  width: auto;
}

.e-btn.e-small.e-rtl .e-icon-right,
.e-css.e-btn.e-small.e-rtl .e-icon-right {
  margin-left: -0.7273em;
  margin-right: 0;
}

.e-btn.e-small.e-rtl .e-icon-left,
.e-css.e-btn.e-small.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: btn-small-icon-margin;
}

.e-btn.e-block,
.e-css.e-btn.e-block {
  display: block;
  width: 100%;
}

.e-small .e-btn,
.e-small.e-btn,
.e-small .e-css.e-btn,
.e-small.e-css.e-btn {
  font-size: 12px;
  line-height: 1.5em;
  padding: 3px 16px;
}

.e-small .e-btn .e-btn-icon,
.e-small.e-btn .e-btn-icon,
.e-small .e-css.e-btn .e-btn-icon,
.e-small.e-css.e-btn .e-btn-icon {
  font-size: 11px;
  width: 0.91em;
}

.e-small .e-btn .e-btn-icon.e-icon-left,
.e-small.e-btn .e-btn-icon.e-icon-left,
.e-small .e-css.e-btn .e-btn-icon.e-icon-left,
.e-small.e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.7273em;
  width: 2.182em;
}

.e-small .e-btn .e-btn-icon.e-icon-right,
.e-small.e-btn .e-btn-icon.e-icon-right,
.e-small .e-css.e-btn .e-btn-icon.e-icon-right,
.e-small.e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.7273em;
  width: 2.182em;
}

.e-small .e-btn .e-btn-icon.e-icon-top,
.e-small.e-btn .e-btn-icon.e-icon-top,
.e-small .e-css.e-btn .e-btn-icon.e-icon-top,
.e-small.e-css.e-btn .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-small .e-btn .e-btn-icon.e-icon-bottom,
.e-small.e-btn .e-btn-icon.e-icon-bottom,
.e-small .e-css.e-btn .e-btn-icon.e-icon-bottom,
.e-small.e-css.e-btn .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-small .e-btn.e-icon-btn,
.e-small.e-btn.e-icon-btn,
.e-small .e-css.e-btn.e-icon-btn,
.e-small.e-css.e-btn.e-icon-btn {
  padding: 3px 6px;
}

.e-small .e-btn.e-top-icon-btn, .e-small .e-btn.e-bottom-icon-btn,
.e-small.e-btn.e-top-icon-btn,
.e-small.e-btn.e-bottom-icon-btn,
.e-small .e-css.e-btn.e-top-icon-btn,
.e-small .e-css.e-btn.e-bottom-icon-btn,
.e-small.e-css.e-btn.e-top-icon-btn,
.e-small.e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 8px 20px;
}

.e-small .e-btn.e-round,
.e-small.e-btn.e-round,
.e-small .e-css.e-btn.e-round,
.e-small.e-css.e-btn.e-round {
  height: 2.5em;
  line-height: 1;
  padding: 0;
  width: 2.5em;
}

.e-small .e-btn.e-round .e-btn-icon,
.e-small.e-btn.e-round .e-btn-icon,
.e-small .e-css.e-btn.e-round .e-btn-icon,
.e-small.e-css.e-btn.e-round .e-btn-icon {
  font-size: 14px;
  line-height: 16px;
  width: auto;
}

.e-small .e-btn.e-rtl .e-icon-right,
.e-small.e-btn.e-rtl .e-icon-right,
.e-small .e-css.e-btn.e-rtl .e-icon-right,
.e-small.e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.7273em;
  margin-right: 0;
}

.e-small .e-btn.e-rtl .e-icon-left,
.e-small.e-btn.e-rtl .e-icon-left,
.e-small .e-css.e-btn.e-rtl .e-icon-left,
.e-small.e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: btn-small-icon-margin;
}

.e-bigger.e-small .e-btn,
.e-bigger.e-small.e-btn,
.e-bigger.e-small .e-css.e-btn,
.e-bigger.e-small.e-css.e-btn {
  font-size: 16px;
  line-height: 2em;
  padding: 1px 22px;
}

.e-bigger.e-small .e-btn .e-btn-icon,
.e-bigger.e-small.e-btn .e-btn-icon,
.e-bigger.e-small .e-css.e-btn .e-btn-icon,
.e-bigger.e-small.e-css.e-btn .e-btn-icon {
  font-size: 15px;
  width: 1.0667em;
}

.e-bigger.e-small .e-btn .e-btn-icon.e-icon-left,
.e-bigger.e-small.e-btn .e-btn-icon.e-icon-left,
.e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-left,
.e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.5334em;
  width: 2.1334em;
}

.e-bigger.e-small .e-btn .e-btn-icon.e-icon-right,
.e-bigger.e-small.e-btn .e-btn-icon.e-icon-right,
.e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-right,
.e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.5334em;
  width: 2.1334em;
}

.e-bigger.e-small .e-btn .e-btn-icon.e-icon-top,
.e-bigger.e-small.e-btn .e-btn-icon.e-icon-top,
.e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-top,
.e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-bigger.e-small .e-btn .e-btn-icon.e-icon-bottom,
.e-bigger.e-small.e-btn .e-btn-icon.e-icon-bottom,
.e-bigger.e-small .e-css.e-btn .e-btn-icon.e-icon-bottom,
.e-bigger.e-small.e-css.e-btn .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-bigger.e-small .e-btn.e-icon-btn,
.e-bigger.e-small.e-btn.e-icon-btn,
.e-bigger.e-small .e-css.e-btn.e-icon-btn,
.e-bigger.e-small.e-css.e-btn.e-icon-btn {
  padding: 1px 9px;
}

.e-bigger.e-small .e-btn.e-top-icon-btn, .e-bigger.e-small .e-btn.e-bottom-icon-btn,
.e-bigger.e-small.e-btn.e-top-icon-btn,
.e-bigger.e-small.e-btn.e-bottom-icon-btn,
.e-bigger.e-small .e-css.e-btn.e-top-icon-btn,
.e-bigger.e-small .e-css.e-btn.e-bottom-icon-btn,
.e-bigger.e-small.e-css.e-btn.e-top-icon-btn,
.e-bigger.e-small.e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 10px 24px;
}

.e-bigger.e-small .e-btn.e-round,
.e-bigger.e-small.e-btn.e-round,
.e-bigger.e-small .e-css.e-btn.e-round,
.e-bigger.e-small.e-css.e-btn.e-round {
  height: 2.5em;
  line-height: 1;
  padding: 0;
  width: 2.5em;
}

.e-bigger.e-small .e-btn.e-round .e-btn-icon,
.e-bigger.e-small.e-btn.e-round .e-btn-icon,
.e-bigger.e-small .e-css.e-btn.e-round .e-btn-icon,
.e-bigger.e-small.e-css.e-btn.e-round .e-btn-icon {
  font-size: 18px;
  line-height: 2.11111em;
  width: auto;
}

.e-bigger.e-small .e-btn.e-rtl .e-icon-right,
.e-bigger.e-small.e-btn.e-rtl .e-icon-right,
.e-bigger.e-small .e-css.e-btn.e-rtl .e-icon-right,
.e-bigger.e-small.e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.5334em;
  margin-right: 0;
}

.e-bigger.e-small .e-btn.e-rtl .e-icon-left,
.e-bigger.e-small.e-btn.e-rtl .e-icon-left,
.e-bigger.e-small .e-css.e-btn.e-rtl .e-icon-left,
.e-bigger.e-small.e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.5334em;
}

.e-bigger .e-btn,
.e-bigger.e-btn,
.e-bigger .e-css.e-btn,
.e-bigger.e-css.e-btn {
  font-size: 16px;
  line-height: 2em;
  padding: 3px 24px;
}

.e-bigger .e-btn .e-btn-icon,
.e-bigger.e-btn .e-btn-icon,
.e-bigger .e-css.e-btn .e-btn-icon,
.e-bigger.e-css.e-btn .e-btn-icon {
  font-size: 16px;
  width: 1em;
}

.e-bigger .e-btn .e-btn-icon.e-icon-left,
.e-bigger.e-btn .e-btn-icon.e-icon-left,
.e-bigger .e-css.e-btn .e-btn-icon.e-icon-left,
.e-bigger.e-css.e-btn .e-btn-icon.e-icon-left {
  margin-left: -0.688em;
  width: 2.25em;
}

.e-bigger .e-btn .e-btn-icon.e-icon-right,
.e-bigger.e-btn .e-btn-icon.e-icon-right,
.e-bigger .e-css.e-btn .e-btn-icon.e-icon-right,
.e-bigger.e-css.e-btn .e-btn-icon.e-icon-right {
  margin-right: -0.688em;
  width: 2.25em;
}

.e-bigger .e-btn .e-btn-icon.e-icon-top,
.e-bigger.e-btn .e-btn-icon.e-icon-top,
.e-bigger .e-css.e-btn .e-btn-icon.e-icon-top,
.e-bigger.e-css.e-btn .e-btn-icon.e-icon-top {
  padding-bottom: 10px;
  width: auto;
}

.e-bigger .e-btn .e-btn-icon.e-icon-bottom,
.e-bigger.e-btn .e-btn-icon.e-icon-bottom,
.e-bigger .e-css.e-btn .e-btn-icon.e-icon-bottom,
.e-bigger.e-css.e-btn .e-btn-icon.e-icon-bottom {
  padding-top: 10px;
  width: auto;
}

.e-bigger .e-btn.e-icon-btn,
.e-bigger.e-btn.e-icon-btn,
.e-bigger .e-css.e-btn.e-icon-btn,
.e-bigger.e-css.e-btn.e-icon-btn {
  padding: 3px 11px;
}

.e-bigger .e-btn.e-top-icon-btn, .e-bigger .e-btn.e-bottom-icon-btn,
.e-bigger.e-btn.e-top-icon-btn,
.e-bigger.e-btn.e-bottom-icon-btn,
.e-bigger .e-css.e-btn.e-top-icon-btn,
.e-bigger .e-css.e-btn.e-bottom-icon-btn,
.e-bigger.e-css.e-btn.e-top-icon-btn,
.e-bigger.e-css.e-btn.e-bottom-icon-btn {
  line-height: 1;
  padding: 10px 24px;
}

.e-bigger .e-btn.e-round,
.e-bigger.e-btn.e-round,
.e-bigger .e-css.e-btn.e-round,
.e-bigger.e-css.e-btn.e-round {
  height: 46px;
  line-height: 1;
  padding: 0;
  width: 46px;
}

.e-bigger .e-btn.e-round .e-btn-icon,
.e-bigger.e-btn.e-round .e-btn-icon,
.e-bigger .e-css.e-btn.e-round .e-btn-icon,
.e-bigger.e-css.e-btn.e-round .e-btn-icon {
  font-size: 20px;
  line-height: 2.2em;
  width: auto;
}

.e-bigger .e-btn.e-rtl .e-icon-right,
.e-bigger.e-btn.e-rtl .e-icon-right,
.e-bigger .e-css.e-btn.e-rtl .e-icon-right,
.e-bigger.e-css.e-btn.e-rtl .e-icon-right {
  margin-left: -0.688em;
  margin-right: 0;
}

.e-bigger .e-btn.e-rtl .e-icon-left,
.e-bigger.e-btn.e-rtl .e-icon-left,
.e-bigger .e-css.e-btn.e-rtl .e-icon-left,
.e-bigger.e-css.e-btn.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.688em;
}

.e-bigger .e-btn.e-small,
.e-bigger.e-btn.e-small,
.e-bigger .e-css.e-btn.e-small,
.e-bigger.e-css.e-btn.e-small {
  font-size: 16px;
  line-height: 2em;
  padding: 1px 22px;
}

.e-bigger .e-btn.e-small .e-btn-icon,
.e-bigger.e-btn.e-small .e-btn-icon,
.e-bigger .e-css.e-btn.e-small .e-btn-icon,
.e-bigger.e-css.e-btn.e-small .e-btn-icon {
  font-size: 15px;
  width: 1.0667em;
}

.e-bigger .e-btn.e-small .e-btn-icon.e-icon-left,
.e-bigger.e-btn.e-small .e-btn-icon.e-icon-left,
.e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-left,
.e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-left {
  margin-left: -0.5334em;
  width: 2.1334em;
}

.e-bigger .e-btn.e-small .e-btn-icon.e-icon-right,
.e-bigger.e-btn.e-small .e-btn-icon.e-icon-right,
.e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-right,
.e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-right {
  margin-right: -0.5334em;
  width: 2.1334em;
}

.e-bigger .e-btn.e-small .e-btn-icon.e-icon-top,
.e-bigger.e-btn.e-small .e-btn-icon.e-icon-top,
.e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-top,
.e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-top {
  padding-bottom: 6px;
  width: auto;
}

.e-bigger .e-btn.e-small .e-btn-icon.e-icon-bottom,
.e-bigger.e-btn.e-small .e-btn-icon.e-icon-bottom,
.e-bigger .e-css.e-btn.e-small .e-btn-icon.e-icon-bottom,
.e-bigger.e-css.e-btn.e-small .e-btn-icon.e-icon-bottom {
  padding-top: 6px;
  width: auto;
}

.e-bigger .e-btn.e-small.e-icon-btn,
.e-bigger.e-btn.e-small.e-icon-btn,
.e-bigger .e-css.e-btn.e-small.e-icon-btn,
.e-bigger.e-css.e-btn.e-small.e-icon-btn {
  padding: 1px 9px;
}

.e-bigger .e-btn.e-small.e-top-icon-btn, .e-bigger .e-btn.e-small.e-bottom-icon-btn,
.e-bigger.e-btn.e-small.e-top-icon-btn,
.e-bigger.e-btn.e-small.e-bottom-icon-btn,
.e-bigger .e-css.e-btn.e-small.e-top-icon-btn,
.e-bigger .e-css.e-btn.e-small.e-bottom-icon-btn,
.e-bigger.e-css.e-btn.e-small.e-top-icon-btn,
.e-bigger.e-css.e-btn.e-small.e-bottom-icon-btn {
  line-height: 1;
  padding: 10px 24px;
}

.e-bigger .e-btn.e-small.e-round,
.e-bigger.e-btn.e-small.e-round,
.e-bigger .e-css.e-btn.e-small.e-round,
.e-bigger.e-css.e-btn.e-small.e-round {
  height: 2.5em;
  line-height: 1;
  padding: 0;
  width: 2.5em;
}

.e-bigger .e-btn.e-small.e-round .e-btn-icon,
.e-bigger.e-btn.e-small.e-round .e-btn-icon,
.e-bigger .e-css.e-btn.e-small.e-round .e-btn-icon,
.e-bigger.e-css.e-btn.e-small.e-round .e-btn-icon {
  font-size: 18px;
  line-height: 2.11111em;
  width: auto;
}

.e-bigger .e-btn.e-small.e-rtl .e-icon-right,
.e-bigger.e-btn.e-small.e-rtl .e-icon-right,
.e-bigger .e-css.e-btn.e-small.e-rtl .e-icon-right,
.e-bigger.e-css.e-btn.e-small.e-rtl .e-icon-right {
  margin-left: -0.5334em;
  margin-right: 0;
}

.e-bigger .e-btn.e-small.e-rtl .e-icon-left,
.e-bigger.e-btn.e-small.e-rtl .e-icon-left,
.e-bigger .e-css.e-btn.e-small.e-rtl .e-icon-left,
.e-bigger.e-css.e-btn.e-small.e-rtl .e-icon-left {
  margin-left: 0;
  margin-right: -0.5334em;
}

/*! button theme */
.e-btn,
.e-css.e-btn {
  -webkit-tap-highlight-color: transparent;
  background-color: #000;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
}

.e-btn:hover,
.e-css.e-btn:hover {
  background-color: #685708;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn:focus,
.e-css.e-btn:focus {
  background-color: #000;
  border-color: #fff;
  color: #fff;
  outline: #fff 1px solid;
  outline-offset: 2px;
  box-shadow: none;
}

.e-btn:active,
.e-css.e-btn:active {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
  outline: #fff 0 solid;
  outline-offset: 0;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
}

.e-btn.e-active,
.e-css.e-btn.e-active {
  background-color: #ffd939;
  border-color: #ffd939;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
  color: #000;
}

.e-btn:disabled,
.e-css.e-btn:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn .e-ripple-element,
.e-css.e-btn .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-round,
.e-css.e-btn.e-round {
  background-color: #000;
  border-color: #fff;
  color: #fff;
}

.e-btn.e-round:hover,
.e-css.e-btn.e-round:hover {
  background-color: #685708;
  border-color: #fff;
  color: #fff;
}

.e-btn.e-round:focus,
.e-css.e-btn.e-round:focus {
  background-color: #000;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
  outline: none;
  outline-offset: 2px;
}

.e-btn.e-round:active,
.e-css.e-btn.e-round:active {
  background-color: #ffd939;
  border-color: #ffd939;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
  color: #000;
  outline: #fff 0 solid;
  outline-offset: 0;
}

.e-btn.e-round:disabled,
.e-css.e-btn.e-round:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-round.e-primary:focus,
.e-css.e-btn.e-round.e-primary:focus {
  outline: none;
}

.e-btn.e-round.e-success:focus,
.e-css.e-btn.e-round.e-success:focus {
  outline: none;
}

.e-btn.e-round.e-info:focus,
.e-css.e-btn.e-round.e-info:focus {
  outline: none;
}

.e-btn.e-round.e-warning:focus,
.e-css.e-btn.e-round.e-warning:focus {
  outline: none;
}

.e-btn.e-round.e-danger:focus,
.e-css.e-btn.e-round.e-danger:focus {
  outline: none;
}

.e-btn.e-primary,
.e-css.e-btn.e-primary {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
}

.e-btn.e-primary:hover,
.e-css.e-btn.e-primary:hover {
  background-color: #685708;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-primary:focus,
.e-css.e-btn.e-primary:focus {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
  outline: #ffd939 1px solid;
  box-shadow: none;
}

.e-btn.e-primary:active,
.e-css.e-btn.e-primary:active {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
  outline: #fff 0 solid;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
}

.e-btn.e-primary.e-active,
.e-css.e-btn.e-primary.e-active {
  background-color: #ffd939;
  border-color: #ffd939;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
  color: #000;
}

.e-btn.e-primary:disabled,
.e-css.e-btn.e-primary:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-primary .e-ripple-element,
.e-css.e-btn.e-primary .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-success,
.e-css.e-btn.e-success {
  background-color: #166600;
  border-color: #166600;
  color: #fff;
}

.e-btn.e-success:hover,
.e-css.e-btn.e-success:hover {
  background-color: #166600;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-success:focus,
.e-css.e-btn.e-success:focus {
  background-color: #166600;
  border-color: #166600;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-success:active, .e-btn.e-success.e-active,
.e-css.e-btn.e-success:active,
.e-css.e-btn.e-success.e-active {
  background-color: #fff;
  border-color: #fff;
  color: #166600;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
}

.e-btn.e-success:disabled,
.e-css.e-btn.e-success:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-success .e-ripple-element,
.e-css.e-btn.e-success .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-info,
.e-css.e-btn.e-info {
  background-color: #0056b3;
  border-color: #0056b3;
  color: #fff;
}

.e-btn.e-info:hover,
.e-css.e-btn.e-info:hover {
  background-color: #0056b3;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-info:focus,
.e-css.e-btn.e-info:focus {
  background-color: #0056b3;
  border-color: #0056b3;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-info:active, .e-btn.e-info.e-active,
.e-css.e-btn.e-info:active,
.e-css.e-btn.e-info.e-active {
  background-color: #fff;
  border-color: #fff;
  color: #0056b3;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
}

.e-btn.e-info:disabled,
.e-css.e-btn.e-info:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-info .e-ripple-element,
.e-css.e-btn.e-info .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-warning,
.e-css.e-btn.e-warning {
  background-color: #944000;
  border-color: #944000;
  color: #fff;
}

.e-btn.e-warning:hover,
.e-css.e-btn.e-warning:hover {
  background-color: #944000;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-warning:focus,
.e-css.e-btn.e-warning:focus {
  background-color: #944000;
  border-color: #944000;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-warning:active, .e-btn.e-warning.e-active,
.e-css.e-btn.e-warning:active,
.e-css.e-btn.e-warning.e-active {
  background-color: #fff;
  border-color: #fff;
  color: #944000;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
}

.e-btn.e-warning:disabled,
.e-css.e-btn.e-warning:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-warning .e-ripple-element,
.e-css.e-btn.e-warning .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-danger,
.e-css.e-btn.e-danger {
  background-color: #b30900;
  border-color: #b30900;
  color: #fff;
}

.e-btn.e-danger:hover,
.e-css.e-btn.e-danger:hover {
  background-color: #b30900;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-danger:focus,
.e-css.e-btn.e-danger:focus {
  background-color: #b30900;
  border-color: #b30900;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-danger:active,
.e-css.e-btn.e-danger:active {
  background-color: #fff;
  border-color: #fff;
  color: #b30900;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
}

.e-btn.e-danger.e-active,
.e-css.e-btn.e-danger.e-active {
  background-color: #fff;
  border-color: #fff;
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2);
  color: #b30900;
}

.e-btn.e-danger:disabled,
.e-css.e-btn.e-danger:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-danger .e-ripple-element,
.e-css.e-btn.e-danger .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-flat,
.e-css.e-btn.e-flat {
  background-color: #000;
  border-color: #000;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-flat:hover,
.e-css.e-btn.e-flat:hover {
  background-color: #685708;
  border-color: #000;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-flat:focus,
.e-css.e-btn.e-flat:focus {
  background-color: #000;
  border-color: #000;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-flat:active, .e-btn.e-flat.e-active,
.e-css.e-btn.e-flat:active,
.e-css.e-btn.e-flat.e-active {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
  box-shadow: none;
}

.e-btn.e-flat:disabled,
.e-css.e-btn.e-flat:disabled {
  background-color: #000;
  border-color: #000;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-flat .e-ripple-element,
.e-css.e-btn.e-flat .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-flat.e-primary,
.e-css.e-btn.e-flat.e-primary {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
}

.e-btn.e-flat.e-primary:hover,
.e-css.e-btn.e-flat.e-primary:hover {
  background-color: #685708;
  border-color: #685708;
  color: #fff;
}

.e-btn.e-flat.e-primary:focus,
.e-css.e-btn.e-flat.e-primary:focus {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
}

.e-btn.e-flat.e-primary:active, .e-btn.e-flat.e-primary.e-active,
.e-css.e-btn.e-flat.e-primary:active,
.e-css.e-btn.e-flat.e-primary.e-active {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
}

.e-btn.e-flat.e-primary:disabled,
.e-css.e-btn.e-flat.e-primary:disabled {
  background-color: #000;
  border-color: #000;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-flat.e-primary .e-ripple-element,
.e-css.e-btn.e-flat.e-primary .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-flat.e-success,
.e-css.e-btn.e-flat.e-success {
  background-color: #166600;
  border-color: #166600;
  color: #fff;
}

.e-btn.e-flat.e-success:hover,
.e-css.e-btn.e-flat.e-success:hover {
  background-color: #166600;
  border-color: #166600;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-flat.e-success:focus,
.e-css.e-btn.e-flat.e-success:focus {
  background-color: #166600;
  border-color: #166600;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-flat.e-success:active, .e-btn.e-flat.e-success.e-active,
.e-css.e-btn.e-flat.e-success:active,
.e-css.e-btn.e-flat.e-success.e-active {
  background-color: #fff;
  border-color: #fff;
  color: #166600;
  box-shadow: none;
}

.e-btn.e-flat.e-success:disabled,
.e-css.e-btn.e-flat.e-success:disabled {
  background-color: #000;
  border-color: #757575;
  color: #757575;
}

.e-btn.e-flat.e-success .e-ripple-element,
.e-css.e-btn.e-flat.e-success .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-flat.e-info,
.e-css.e-btn.e-flat.e-info {
  background-color: #0056b3;
  border-color: #0056b3;
  color: #fff;
}

.e-btn.e-flat.e-info:hover,
.e-css.e-btn.e-flat.e-info:hover {
  background-color: #0056b3;
  border-color: #0056b3;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-flat.e-info:focus,
.e-css.e-btn.e-flat.e-info:focus {
  background-color: #0056b3;
  border-color: #0056b3;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-flat.e-info:active, .e-btn.e-flat.e-info.e-active,
.e-css.e-btn.e-flat.e-info:active,
.e-css.e-btn.e-flat.e-info.e-active {
  background-color: #fff;
  border-color: #fff;
  color: #0056b3;
  box-shadow: none;
}

.e-btn.e-flat.e-info:disabled,
.e-css.e-btn.e-flat.e-info:disabled {
  background-color: #000;
  border-color: #757575;
  color: #757575;
}

.e-btn.e-flat.e-info .e-ripple-element,
.e-css.e-btn.e-flat.e-info .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-flat.e-warning,
.e-css.e-btn.e-flat.e-warning {
  background-color: #944000;
  border-color: #944000;
  color: #fff;
}

.e-btn.e-flat.e-warning:hover,
.e-css.e-btn.e-flat.e-warning:hover {
  background-color: #944000;
  border-color: #944000;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-flat.e-warning:focus,
.e-css.e-btn.e-flat.e-warning:focus {
  background-color: #944000;
  border-color: #944000;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-flat.e-warning:active, .e-btn.e-flat.e-warning.e-active,
.e-css.e-btn.e-flat.e-warning:active,
.e-css.e-btn.e-flat.e-warning.e-active {
  background-color: #fff;
  border-color: #fff;
  color: #944000;
  box-shadow: none;
}

.e-btn.e-flat.e-warning:disabled,
.e-css.e-btn.e-flat.e-warning:disabled {
  background-color: #000;
  border-color: #757575;
  color: #757575;
}

.e-btn.e-flat.e-warning .e-ripple-element,
.e-css.e-btn.e-flat.e-warning .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-flat.e-danger,
.e-css.e-btn.e-flat.e-danger {
  background-color: #b30900;
  border-color: #000;
  color: #fff;
}

.e-btn.e-flat.e-danger:hover,
.e-css.e-btn.e-flat.e-danger:hover {
  background-color: #b30900;
  border-color: #b30900;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-flat.e-danger:focus,
.e-css.e-btn.e-flat.e-danger:focus {
  background-color: #b30900;
  border-color: #b30900;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-flat.e-danger:active, .e-btn.e-flat.e-danger.e-active,
.e-css.e-btn.e-flat.e-danger:active,
.e-css.e-btn.e-flat.e-danger.e-active {
  background-color: #fff;
  border-color: #fff;
  color: #b30900;
  box-shadow: none;
}

.e-btn.e-flat.e-danger:disabled,
.e-css.e-btn.e-flat.e-danger:disabled {
  background-color: #000;
  border-color: #000;
  color: #b71c1c;
}

.e-btn.e-flat.e-danger .e-ripple-element,
.e-css.e-btn.e-flat.e-danger .e-ripple-element {
  background-color: transparent;
}

.e-btn.e-outline,
.e-css.e-btn.e-outline {
  background-color: #000;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-outline:hover,
.e-css.e-btn.e-outline:hover {
  background-color: #685708;
  border-color: #fff;
  box-shadow: none;
  color: #fff;
}

.e-btn.e-outline:focus,
.e-css.e-btn.e-outline:focus {
  background-color: #000;
  border-color: #fff;
  color: #fff;
  box-shadow: none;
}

.e-btn.e-outline:active, .e-btn.e-outline.e-active,
.e-css.e-btn.e-outline:active,
.e-css.e-btn.e-outline.e-active {
  background-color: #ffd939;
  border-color: #ffd939;
  box-shadow: none;
  color: #000;
}

.e-btn.e-outline:disabled,
.e-css.e-btn.e-outline:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-outline.e-primary,
.e-css.e-btn.e-outline.e-primary {
  background-color: #000;
  border-color: #ffd939;
  color: #ffd939;
}

.e-btn.e-outline.e-primary:hover,
.e-css.e-btn.e-outline.e-primary:hover {
  background-color: #685708;
  border-color: #fff;
  color: #fff;
}

.e-btn.e-outline.e-primary:focus,
.e-css.e-btn.e-outline.e-primary:focus {
  background-color: #ffd939;
  border-color: #ffd939;
  color: #000;
}

.e-btn.e-outline.e-primary:active, .e-btn.e-outline.e-primary.e-active,
.e-css.e-btn.e-outline.e-primary:active,
.e-css.e-btn.e-outline.e-primary.e-active {
  background-color: #ffd939;
  border-color: #ffd939;
  box-shadow: none;
  color: #000;
}

.e-btn.e-outline.e-primary:disabled,
.e-css.e-btn.e-outline.e-primary:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-outline.e-success,
.e-css.e-btn.e-outline.e-success {
  background-color: #000;
  border-color: #166600;
  color: #166600;
}

.e-btn.e-outline.e-success:hover,
.e-css.e-btn.e-outline.e-success:hover {
  background-color: #166600;
  border-color: #fff;
  color: #fff;
}

.e-btn.e-outline.e-success:focus,
.e-css.e-btn.e-outline.e-success:focus {
  background-color: #166600;
  border-color: #166600;
  color: #fff;
}

.e-btn.e-outline.e-success:active, .e-btn.e-outline.e-success.e-active,
.e-css.e-btn.e-outline.e-success:active,
.e-css.e-btn.e-outline.e-success.e-active {
  background-color: #fff;
  border-color: #fff;
  box-shadow: none;
  color: #166600;
}

.e-btn.e-outline.e-success:disabled,
.e-css.e-btn.e-outline.e-success:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-outline.e-info,
.e-css.e-btn.e-outline.e-info {
  background-color: #000;
  border-color: #0056b3;
  color: #0056b3;
}

.e-btn.e-outline.e-info:hover,
.e-css.e-btn.e-outline.e-info:hover {
  background-color: #0056b3;
  border-color: #fff;
  color: #fff;
}

.e-btn.e-outline.e-info:focus,
.e-css.e-btn.e-outline.e-info:focus {
  background-color: #0056b3;
  border-color: #0056b3;
  color: #fff;
}

.e-btn.e-outline.e-info:active, .e-btn.e-outline.e-info.e-active,
.e-css.e-btn.e-outline.e-info:active,
.e-css.e-btn.e-outline.e-info.e-active {
  background-color: #fff;
  border-color: #fff;
  box-shadow: none;
  color: #0056b3;
}

.e-btn.e-outline.e-info:disabled,
.e-css.e-btn.e-outline.e-info:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-outline.e-warning,
.e-css.e-btn.e-outline.e-warning {
  background-color: #000;
  border-color: #944000;
  color: #944000;
}

.e-btn.e-outline.e-warning:hover,
.e-css.e-btn.e-outline.e-warning:hover {
  background-color: #944000;
  border-color: #fff;
  color: #fff;
}

.e-btn.e-outline.e-warning:focus,
.e-css.e-btn.e-outline.e-warning:focus {
  background-color: #944000;
  border-color: #944000;
  color: #fff;
}

.e-btn.e-outline.e-warning:active, .e-btn.e-outline.e-warning.e-active,
.e-css.e-btn.e-outline.e-warning:active,
.e-css.e-btn.e-outline.e-warning.e-active {
  background-color: #fff;
  border-color: #fff;
  box-shadow: none;
  color: #944000;
}

.e-btn.e-outline.e-warning:disabled,
.e-css.e-btn.e-outline.e-warning:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-outline.e-danger,
.e-css.e-btn.e-outline.e-danger {
  background-color: #000;
  border-color: #b30900;
  color: #b30900;
}

.e-btn.e-outline.e-danger:hover,
.e-css.e-btn.e-outline.e-danger:hover {
  background-color: #b30900;
  border-color: #fff;
  color: #fff;
}

.e-btn.e-outline.e-danger:focus,
.e-css.e-btn.e-outline.e-danger:focus {
  background-color: #b30900;
  border-color: #b30900;
  color: #fff;
}

.e-btn.e-outline.e-danger:active, .e-btn.e-outline.e-danger.e-active,
.e-css.e-btn.e-outline.e-danger:active,
.e-css.e-btn.e-outline.e-danger.e-active {
  background-color: #fff;
  border-color: #fff;
  box-shadow: none;
  color: #b30900;
}

.e-btn.e-outline.e-danger:disabled,
.e-css.e-btn.e-outline.e-danger:disabled {
  background-color: #000;
  border-color: #757575;
  box-shadow: none;
  color: #757575;
}

.e-btn.e-link,
.e-css.e-btn.e-link {
  background-color: transparent;
  border-color: transparent;
  border-radius: 0;
  box-shadow: none;
  color: #8a8aff;
}

.e-btn.e-link:hover,
.e-css.e-btn.e-link:hover {
  border-radius: 0;
  color: #8a8aff;
  text-decoration: underline;
}

.e-btn.e-link:focus,
.e-css.e-btn.e-link:focus {
  border-radius: 0;
  text-decoration: underline;
  color: #8a8aff;
}

.e-btn.e-link:disabled,
.e-css.e-btn.e-link:disabled {
  color: #757575;
  background-color: transparent;
  box-shadow: none;
  text-decoration: none;
}
