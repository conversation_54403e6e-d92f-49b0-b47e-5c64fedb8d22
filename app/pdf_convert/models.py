import uuid
from faker import Faker
from datetime import date

from sqlalchemy import Column, Integer, String, Text, Time, TIMESTAMP, ForeignKey, Date, LargeBinary, UUID, func
from sqlalchemy.orm import relationship

from app import db

# Initialize Faker
fake = Faker()


class PasPt(db.Model):
    __tablename__ = 'pas_pt'

    patientid = Column(Integer, primary_key=True)
    ur = Column(String, nullable=False)
    ur_hsid = Column(String, nullable=False)
    dob = Column(Date)
    gender_code = Column(String)
    gender_forrfts_code = Column(String)
    email = Column(String)
    phone_home = Column(String)
    phone_mobile = Column(String)
    phone_work = Column(String)
    countryofbirth_code = Column(String)
    preferredlanguage_code = Column(String)
    aboriginalstatus_code = Column(String)
    medicare_no = Column(String)
    medicare_expirydate = Column(String)
    death_indicator = Column(String)
    death_date = Column(String)
    race_forrfts_code = Column(String)
    site_id = Column(Integer)
    # created = db.Column(db.DateTime, server_default=func.now())
    # updated = db.Column(db.DateTime, server_default=func.now())
    names = relationship("PasPtName", back_populates="patient", cascade="all, delete-orphan")
    addresses = relationship("PasPtAddress", back_populates="patient", cascade="all, delete-orphan")
    ur_numbers = relationship("PasPtUrNumber", back_populates="patient", cascade="all, delete-orphan")

    @classmethod
    def create_fake(cls, site_id):
        """Create a fake PasPt patient with related data"""

        gender = 'M'  # fake.random_element(elements=('M', 'F'))

        # Create the main patient record
        patient = cls(
            ur=fake.unique.random_number(digits=8),
            ur_hsid=fake.unique.random_number(digits=10),
            dob=date(1957, 6, 22),
            gender_code=gender,
            gender_forrfts_code='1' if gender == 'M' else '2',
            email=fake.email(),
            phone_home=fake.phone_number(),
            aboriginalstatus_code='9',
            death_indicator='N',
            death_date=None,
            race_forrfts_code='1',
            site_id=site_id
        )

        # Add to session and flush to get the patientid
        db.session.add(patient)
        db.session.flush()

        # Create related name record
        name = PasPtName.create_fake(patient.patientid)
        patient.names.append(name)

        # Create related address record
        address = PasPtAddress.create_fake(patient.patientid)
        patient.addresses.append(address)

        # Create related UR number record
        ur_number = PasPtUrNumber.create_fake(patient.patientid, patient.ur, patient.ur_hsid)
        patient.ur_numbers.append(ur_number)

        return patient


class PasPtName(db.Model):
    __tablename__ = 'pas_pt_names'

    nameid = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    name_type = Column(String, default='primary')
    title = Column(String)
    firstname = Column(String, nullable=False)
    surname = Column(String, nullable=False)
    middlename = Column(String)

    patient = relationship("PasPt", back_populates="names")

    @classmethod
    def create_fake(cls, patientid, gender=None):
        """Create a fake PasPtName record"""

        if not gender:
            gender = fake.random_element(elements=('M', 'F'))

        if gender == 'M':
            title = fake.random_element(elements=('Mr', None))
            firstname = fake.first_name_male()
            lastname = fake.last_name_male()
        else:
            title = fake.random_element(elements=('Mrs', 'Ms'))
            firstname = fake.first_name_female()
            lastname = fake.last_name_female()

        return cls(
            patientid=patientid,
            name_type=fake.random_element(elements=('primary',)),
            title=title,
            firstname=firstname,
            surname=lastname,
        )


class PasPtAddress(db.Model):
    __tablename__ = 'pas_pt_address'

    addressid = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    address_type_code = Column(String, default='primary')
    address_1 = Column(String)
    address_2 = Column(String)
    suburb = Column(String)
    postcode = Column(String)

    patient = relationship("PasPt", back_populates="addresses")

    @classmethod
    def create_fake(cls, patientid):
        """Create a fake PasPtAddress record"""
        return cls(
            patientid=patientid,
            address_type_code=fake.random_element(elements=('4', '1', '2', '3')),
            address_1=fake.street_address(),
            address_2=fake.secondary_address() if fake.boolean(chance_of_getting_true=20) else None,
            suburb=fake.city(),
            postcode=fake.postcode()
        )


class DeviceTemplate(db.Model):
    __tablename__ = 'device_templates'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    filename = Column(Text, nullable=False)
    template_s3_key = Column(Text, nullable=False, unique=True)
    instruction_s3_key = Column(Text, nullable=True)
    manufacturer = Column(String, nullable=False)
    device_model = Column(String, nullable=False)
    template_type = Column(String, nullable=False)


class PasPtUrNumber(db.Model):
    __tablename__ = 'pas_pt_ur_numbers'

    ur_id = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    ur = Column(String, nullable=False)
    ur_hsid = Column(String, nullable=False)
    ur_status = Column(String, nullable=False)

    patient = relationship("PasPt", back_populates="ur_numbers")

    @classmethod
    def create_fake(cls, patientid, ur, ur_hsid):
        """Create a fake PasPtUrNumber record"""
        return cls(
            patientid=patientid,
            ur=ur,
            ur_hsid=ur_hsid,
            ur_status='primary'
        )


class AttachmentFile(db.Model):
    __tablename__ = 'attachment_files'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    site_id = Column(Integer, nullable=False)
    patient_id = Column(Integer, nullable=True)
    rft_id = Column(Integer, nullable=True)
    filename = Column(Text, nullable=False)
    s3_key = Column(Text, nullable=False, unique=True)
    uploaded_by = Column(Text, nullable=True)
    uploaded_at = Column(TIMESTAMP(timezone=False), server_default=func.now())
    description = Column(Text, nullable=True)


class RSession(db.Model):
    __tablename__ = 'r_sessions'

    sessionid = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=True)

    testdate = Column(Date)
    lab = Column(String)
    height = Column(String)
    weight = Column(String)

    req_name = Column(String)
    req_address = Column(String)
    req_providernumber = Column(String)
    req_healthservice_text = Column(String)
    req_healthservice_code = Column(String)

    req_date = Column(Date)
    req_time = Column(Time)
    req_phone = Column(String)
    req_fax = Column(String)
    req_email = Column(String)
    req_clinicalnotes = Column(Text)

    smoke_hx = Column(String)
    smoke_cigsperday = Column(String)
    smoke_yearssmoked = Column(String)
    smoke_packyears = Column(String)
    smoke_last = Column(String)

    diagnosticcategory = Column(String)
    pred_sourceids = Column(Text)

    admissionstatus = Column(String)
    report_copyto = Column(String)
    report_copyto_2 = Column(String)

    billing_billingmo = Column(String)
    billing_billingmoproviderno = Column(String)

    lastupdated_session = Column(TIMESTAMP)
    lastupdatedby_session = Column(String)

    # --- Relationships ---
    patient = relationship("PasPt", backref="sessions")

    @classmethod
    def create_fake(cls, patientid):
        """Create a fake RSession record based on real-world examples"""

        # Generate realistic data
        testdate = fake.date_between(start_date='-30d')
        req_date = fake.date_between(start_date=testdate, end_date=testdate)

        # Randomly decide if smoking history should be included
        has_smoking_history = fake.boolean(chance_of_getting_true=40)

        session = cls(
            patientid=patientid,
            testdate=testdate,
            height="172",
            weight="75",

            # Requester information
            req_name=f"Dr. {fake.first_name()} {fake.last_name()}" if fake.boolean(chance_of_getting_true=80) else None,
            req_address=fake.street_address() + ", " + fake.city() + " " + fake.state_abbr() + " " + fake.postcode() if fake.boolean(
                chance_of_getting_true=60) else None,
            req_providernumber=str(fake.unique.random_number(digits=8)) if fake.boolean(
                chance_of_getting_true=70) else None,
            req_healthservice_text=f"{fake.company()} Hospital",
            req_date=req_date,
            req_time=fake.time_object(),
            req_phone=fake.phone_number() if fake.boolean(chance_of_getting_true=60) else None,
            req_fax=fake.phone_number() if fake.boolean(chance_of_getting_true=20) else None,
            req_email=fake.email() if fake.boolean(chance_of_getting_true=50) else None,
            req_clinicalnotes=fake.text(max_nb_chars=200) if fake.boolean(chance_of_getting_true=70) else None,

            # Smoking history
            smoke_hx=fake.random_element(elements=('Y', 'N')) if has_smoking_history else None,
            smoke_cigsperday=str(fake.random_int(min=1, max=40)) if has_smoking_history and fake.boolean(
                chance_of_getting_true=60) else None,
            smoke_yearssmoked=str(fake.random_int(min=1, max=50)) if has_smoking_history and fake.boolean(
                chance_of_getting_true=60) else None,
            smoke_packyears=str(fake.random_int(min=1, max=100)) if has_smoking_history and fake.boolean(
                chance_of_getting_true=60) else None,
            smoke_last=fake.date_between(start_date='-5y', end_date='today').strftime(
                '%Y-%m-%d') if has_smoking_history and fake.boolean(chance_of_getting_true=40) else None,

            # Diagnostic and admission information
            diagnosticcategory=fake.random_element(elements=('Respiratory', 'Cardiovascular', None)),
            admissionstatus=fake.random_element(elements=('Outpatient', 'Inpatient')),
            report_copyto=fake.name() if fake.boolean(chance_of_getting_true=30) else None,
            report_copyto_2=fake.name() if fake.boolean(chance_of_getting_true=20) else None,

            # Timestamps
            lastupdated_session=fake.date_time_between(start_date='-1d', end_date='now'),
            lastupdatedby_session=fake.random_element(
                elements=('user123', 'current_user', 'admin', 'doctor1', 'nurse1'))
        )

        return session


class RftRoutine(db.Model):
    __tablename__ = 'rft_routine'

    rftid = Column(Integer, primary_key=True)
    sessionid = Column(Integer, ForeignKey('r_sessions.sessionid'))
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'))

    testtime = Column(Time)
    testtype = Column(String)
    lab = Column(String)

    report_text = Column(Text)
    report_reportedby = Column(String)
    report_reporteddate = Column(TIMESTAMP)
    report_verifiedby = Column(String)
    report_verifieddate = Column(TIMESTAMP)
    report_authorisedby = Column(String)
    report_authoriseddate = Column(TIMESTAMP)
    report_amendedby = Column(String)
    report_amendeddate = Column(TIMESTAMP)
    report_amendednotes = Column(Text)
    report_status = Column(String)

    # Baseline
    r_bl_fev1 = Column(String)
    r_bl_fvc = Column(String)
    r_bl_vc = Column(String)
    r_bl_fer = Column(String)
    r_bl_fef2575 = Column(String)
    r_bl_pef = Column(String)
    r_pre_condition = Column(String)
    r_bl_tlco = Column(String)
    r_bl_kco = Column(String)
    r_bl_va = Column(String)
    r_bl_hb = Column(String)
    r_bl_ivc = Column(String)
    r_bl_tlc = Column(String)
    r_bl_frc = Column(String)
    r_bl_rv = Column(String)
    r_bl_erv = Column(String)
    r_bl_ic = Column(String)
    r_bl_rvtlc = Column(String)
    r_bl_lvvc = Column(String)
    r_bl_mip = Column(String)
    r_bl_mep = Column(String)
    r_bl_snip = Column(String)
    r_spo2_1 = Column(String)
    r_spo2_2 = Column(String)
    r_bl_feno = Column(String)

    # Post
    r_post_fev1 = Column(String)
    r_post_fvc = Column(String)
    r_post_vc = Column(String)
    r_post_fer = Column(String)
    r_post_fef2575 = Column(String)
    r_post_pef = Column(String)
    r_post_condition = Column(String)

    # Conditions
    r_condition_tl = Column(String)
    r_condition_lv = Column(String)
    r_condition_mrp = Column(String)
    r_condition_feno = Column(String)

    flowvolloop = Column(LargeBinary)  # Base64 encoded image
    bdstatus = Column(String)
    technicalnotes = Column(Text)
    scientist = Column(String)

    # # ABG1
    # r_abg1_fio2 = Column(String)
    r_abg1_ph = Column(String)
    r_abg1_pao2 = Column(String)
    r_abg1_paco2 = Column(String)
    r_abg1_sao2 = Column(String)
    r_abg1_cohb = Column(String)
    r_abg1_be = Column(String)
    r_abg1_hco3 = Column(String)
    r_abg1_aapo2 = Column(String)
    # r_abg1_shunt = Column(String)

    # # ABG2
    r_abg2_fio2 = Column(String)
    r_abg2_ph = Column(String)
    r_abg2_pao2 = Column(String)
    r_abg2_paco2 = Column(String)
    r_abg2_sao2 = Column(String)
    # r_abg2_cohb = Column(String)
    r_abg2_be = Column(String)
    r_abg2_hco3 = Column(String)
    # r_abg2_aapo2 = Column(String)
    r_abg2_shunt = Column(String)
    #
    # # Additional
    lungvolumes_method = Column(String)
    # lastupdated_rft = Column(TIMESTAMP)
    # lastupdatedby_rft = Column(String)
    # sample_type = Column(String)
    # r_abg1_sampletype = Column(String)
    r_abg2_sampletype = Column(String)
    # device_info = Column(Text)

    # --- Relationships ---
    session = db.relationship("RSession", backref="rft_routine")
    patient = db.relationship("PasPt", backref="rft_routine")

    @classmethod
    def create_fake(cls, patient_id, session_id):
        rft_test = cls(
            **{
                "testtype": "RFTs (Sp1 Sp2 TL LV FeNO MRP ABG Sh Ox)",
                "testtime": "17:37:00",
                "technicalnotes": "All tests acceptable and repeatable. ",
                "sessionid": session_id,
                "scientist": "John Hutchinson",
                "report_text": "Baseline ventilatory function is within normal limits with no significant bronchodilator response on this occasion. Carbon monoxide transfer factor, uncorrected for haemoglobin, shows a reduction indicating a gas exchange abnormality at the alveolar-capillary level or due to pulmonary vascular dysfunction. Lung volumes are within normal limits. Maximal respiratory pressures are within the normal range. FeNO result is intermediate and may suggest the presence of active eosinophilic inflammation. Arterial blood gases reveal normoxaemia with a normal (A-a)PO2 gradient and a normal pH.",
                "report_status": "For discussion",
                "report_reporteddate": "2025-06-13T14:00:00",
                "report_reportedby": "dr. bailey, bill",
                "r_spo2_2": "99",
                "r_spo2_1": "96",
                "r_pre_condition": "Baseline",
                "r_post_vc": "5.54",
                "r_post_pef": "8.1",
                "r_post_fvc": "5.18",
                "r_post_fev1": "4.22",
                "r_post_fef2575": "3.7",
                "r_post_condition": "Post BD (Salb MDI)",
                "r_condition_tl": "Baseline",
                "r_condition_lv": "Baseline",
                "r_bl_vc": "5.19",
                "r_bl_va": "6.11",
                "r_bl_tlco": "18.1",
                "r_bl_tlc": "6.55",
                "r_bl_snip": "105",
                "r_bl_rvtlc": "24.274809160305345",
                "r_bl_rv": "1.59",
                "r_bl_pef": "7.2",
                "r_bl_mip": "69",
                "r_bl_mep": "110",
                "r_bl_lvvc": "4.99",
                "r_bl_ivc": "5",
                "r_bl_ic": "2.24",
                "r_bl_hb": "95",
                "r_bl_fvc": "5.22",
                "r_bl_frc": "3.75",
                "r_bl_fev1": "3.91",
                "r_bl_feno": "33",
                "r_bl_fef2575": "3.45",
                "r_bl_erv": "1.15",
                "r_abg2_shunt": "12.141280353200884",
                "r_abg2_sao2": "100",
                "r_abg2_sampletype": "Arterial",
                "r_abg2_ph": "7.39",
                "r_abg2_pao2": "455",
                "r_abg2_paco2": "38",
                "r_abg2_hco3": "20.2",
                "r_abg2_fio2": "100%",
                "r_abg2_be": "1.1",
                "r_abg1_sao2": "97",
                "r_abg1_ph": "7.41",
                "r_abg1_pao2": "88",
                "r_abg1_paco2": "39",
                "r_abg1_hco3": "22.3",
                "r_abg1_cohb": "1.2",
                "r_abg1_be": "1.6",
                "r_abg1_aapo2": "12.6",
                "patientid": patient_id,
                "lungvolumes_method": "Plethysmography",
                "lab": "Main lab",
                "bdstatus": "Nil today",
            }
        )

        return rft_test
