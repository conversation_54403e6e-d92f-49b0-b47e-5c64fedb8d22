import statistics
from typing import List
import logging

from sqlalchemy import text
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy_utils import ChoiceType, Timestamp

from app import db
from audit_mixin import AuditableMixin

logger = logging.getLogger(__name__)


class Lab(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'labs'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    name = db.Column(db.String)
    equipments = db.relationship(
        'Equipment', back_populates='lab'
    )
    site__ref_id = db.Column(db.Integer, db.ForeignKey('site.id'), nullable=True)  # Add foreign key

    site = db.relationship(
        'Site', back_populates='labs'
    )

    def __repr__(self):
        return f"{self.name} (Site: {self.site.name})"

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'site__ref_id': self.site__ref_id,
            "site": self.site.name
        }


class Equipment(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'equipments'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    equipment_manufacturer_id = db.Column(
        db.Integer, db.ForeignKey('equipment_companies.id'), nullable=True
    )
    equipment_distributor_id = db.Column(
        db.Integer, db.ForeignKey('equipment_companies.id'), nullable=True
    )
    model = db.Column(db.String)
    serial_no = db.Column(db.String)
    purchase_date = db.Column(db.DateTime(timezone=True))
    active = db.Column(db.Boolean)
    inactivity_date = db.Column(db.DateTime(timezone=True))
    inactivity_reason = db.Column(db.Text)
    lab_id = db.Column(db.Integer, db.ForeignKey('labs.id'), nullable=False)
    equipment_manufacturer = db.relationship(
        'EquipmentCompany', back_populates='manufactured_equipments', foreign_keys=[equipment_manufacturer_id]
    )
    equipment_distributor = db.relationship(
        'EquipmentCompany', back_populates='distributed_equipments', foreign_keys=[equipment_distributor_id]
    )
    lab = db.relationship(
        'Lab', back_populates='equipments'
    )
    control_methods_utlizing_equipment = db.relationship(
        'ControlMethod', back_populates='controlling_equipment'
    )
    control_methods = db.relationship(
        'ControlMethod', secondary='control_methods_equipments', back_populates='equipments'
    )

    # sessions = db.relationship('Session', back_populates='equipment')

    def __repr__(self):
        return self.model

    def to_dict(self):
        return {
            'id': self.id,
            'model': self.model,
            'serial_no': self.serial_no,
            'purchase_date': self.purchase_date,
            'active': self.active,
            'inactivity_date': self.inactivity_date,
            'inactivity_reason': self.inactivity_reason,
            'lab_id': self.lab_id,
            'equipment_manufacturer_id': self.equipment_manufacturer_id,
            'equipment_distributor_id': self.equipment_distributor_id
        }


class ControlRuleInterval(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'control_rule_intervals'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    control_method_equipment_parameter_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_methods_equipments_parameters.id'
        ), nullable=False
    )
    session_ids = db.Column(ARRAY(db.Integer))

    mean_value = db.Column(db.Float, nullable=True)
    median_value = db.Column(db.Float, nullable=True)
    std_value = db.Column(db.Float, nullable=True)
    values_count = db.Column(db.Integer, default=1)

    @classmethod
    def get_for_session_data(cls, session_data_id):
        session_data = db.session.query(SessionData).get(session_data_id)

        return cls.get_for_session(session_data.session_id, session_data.control_method_equipment_parameter_id)

    @classmethod
    def get_for_session(cls, session_id, control_method_equipment_parameter_id):
        session = db.session.query(Session).get(session_id)
        logger.debug("session session_time %s", session.session_time)

        return cls.query.filter(
            cls.control_method_equipment_parameter_id == control_method_equipment_parameter_id,
            text(
                "(SELECT s.session_time FROM sessions as s WHERE s.id = any(session_ids) ORDER BY session_time ASC "
                "LIMIT 1) <= :param"
            )
            .bindparams(param=session.session_time),
        ).order_by(
            text("(SELECT MIN(s.session_time) FROM sessions as s WHERE s.id = any(session_ids)) DESC"),
        ).first()

    def get_lower_bound_session(self) -> 'Session':
        return Session.query.filter(Session.id.in_(self.session_ids)).order_by(Session.session_time.asc()).first()

    def get_upper_bound_session(self) -> 'Session':
        return Session.query.filter(Session.id.in_(self.session_ids)).order_by(Session.session_time.desc()).first()

    def get_next_interval(self):
        lower_bound_session = self.get_lower_bound_session()

        # Find the next control interval for this parameter
        return ControlRuleInterval.query.filter(
            ControlRuleInterval.control_method_equipment_parameter_id == self.control_method_equipment_parameter_id,
            text(
                "(SELECT s.session_time FROM sessions as s WHERE s.id = any(session_ids) ORDER BY session_time ASC "
                "LIMIT 1) > "
                ":lower_bound"
            )
            .bindparams(lower_bound=lower_bound_session.session_time)
        ).first()

    def get_session_data(self):
        return SessionData.query.filter(
            SessionData.session_id.in_(self.session_ids),
            SessionData.control_method_equipment_parameter_id == self.control_method_equipment_parameter_id,
        ).all()

    def get_session_data_values(self) -> List[float]:
        session_datas = self.get_session_data()

        return [
            session_data.parameter_value
            for session_data in session_datas
            if session_data.parameter_value is not None
        ]

    # NOTE: I tried to treat sqlalchemy as Active Record ORM (live django)
    # But hard learnt lesson is I shouldn't.
    # These methods are written before this learning, and I'm not touching them now

    def calculate_statistics(self):
        values = self.get_session_data_values()
        self.values_count = len(values)
        logger.debug("values %s", values)

        if not values:
            return {"mean": None, "median": None, "std": None}

        mean_val = statistics.mean(values)
        median_val = statistics.median(values)
        std_val = statistics.stdev(values) if len(values) > 1 else 0.0

        self.mean_value = mean_val
        self.median_value = median_val
        self.std_value = std_val

        return {"mean": mean_val, "median": median_val, "std": std_val}

    def save_statistics(self):
        self.calculate_statistics()
        db.session.add(self)
        db.session.commit()

    # def save

    def to_dict(self):
        return {
            'id': self.id,
            'created': self.created.isoformat(),
            'control_method_equipment_parameter_id': self.control_method_equipment_parameter_id,
        }


class EquipmentCompany(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'equipment_companies'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    name = db.Column(db.String)
    address = db.Column(db.Text)
    manufactured_equipments = db.relationship(
        'Equipment', primaryjoin="Equipment.equipment_manufacturer_id==EquipmentCompany.id",
        back_populates='equipment_manufacturer'
    )
    distributed_equipments = db.relationship(
        'Equipment', primaryjoin="Equipment.equipment_distributor_id==EquipmentCompany.id",
        back_populates='equipment_distributor'
    )

    site_id = db.Column(db.Integer, db.ForeignKey('site.id'))  # Add foreign key
    site = db.relationship('Site')

    def __repr__(self):
        return self.name


log_type_options = [
    ('maintenance', 'Maintenance'),
    ('lease', 'Lease')
]


class ControlMethodEquipment(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'control_methods_equipments'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    control_method_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_methods.id'
        )
    )
    equipment_id = db.Column(
        db.Integer, db.ForeignKey(
            'equipments.id'
        )
    )
    control_method_equipment_parameters = db.relationship(
        'ControlMethodEquipmentParameter', back_populates='control_method_equipment'
    )


control_method_type_options = [
    ('biological', 'Biological'),
    ('non-biological', 'Non Biological')
]

gender_options = [
    ('male', 'Male'),
    ('famale', 'Female'),
    ('non-binary', 'Non Binary'),
]


class ControlMethod(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'control_methods'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    control_method_type = db.Column(
        ChoiceType(control_method_type_options), nullable=False
    )
    first_name = db.Column(db.String)
    sur_name = db.Column(db.String)
    gender = db.Column(
        ChoiceType(gender_options), nullable=True
    )
    date_of_birth = db.Column(db.DateTime(timezone=True))
    controlling_equipment_id = db.Column(
        db.Integer, db.ForeignKey('equipments.id'), nullable=True
    )
    controlling_equipment = db.relationship(
        'Equipment', back_populates='control_methods_utlizing_equipment'
    )
    equipments = db.relationship(
        'Equipment', secondary='control_methods_equipments', back_populates='control_methods'
    )

    # parameters = db.relationship(
    #     'Parameter', secondary='control_methods_parameters', back_populates='control_methods')

    def __repr__(self):
        method_name = self.first_name + " " + self.sur_name if self.first_name and self.sur_name \
                    else self.sur_name if self.sur_name \
                    else self.first_name if self.first_name \
                    else "Control Method"

        return (
            f"{self.controlling_equipment.model} - ({self.control_method_type})"
                                    if self.controlling_equipment_id is not None
                    else f"{method_name} - ({self.control_method_type})"
        )

    def to_dict(self):
        return {
            'id': self.id,
            'control_method_type': self.control_method_type and self.control_method_type.code,
            'first_name': self.first_name,
            'sur_name': self.sur_name,
            'gender': self.gender and self.gender.code,
            'date_of_birth': self.date_of_birth,
            'equipment_id': self.controlling_equipment_id,
            # Adjust if needed
            # 'name': self.controlling_equipment.model if self.controlling_equipment else 'Unknown'
            'name': f"{self.first_name if self.first_name else ''} {self.sur_name if self.sur_name else ''}"
        }


class ControlMethodEquipmentParameter(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'control_methods_equipments_parameters'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    control_method_equipment_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_methods_equipments.id'
        )
    )
    parameter_id = db.Column(
        db.Integer, db.ForeignKey(
            'parameters.id'
        )
    )
    session_datas = db.relationship(
        'SessionData', back_populates='control_method_equipment_parameter'
    )
    parameter = db.relationship(
        'Parameter', back_populates='control_method_equipment_parameters'
    )
    control_method_equipment = db.relationship(
        'ControlMethodEquipment', back_populates='control_method_equipment_parameters'
    )

    control_method_equipment_parameter_controlrule = db.relationship(
        'ControlMethodEquipmentParameterControlRule', back_populates='control_method_equipment_parameter'
    )

    # def __repr__(self):
    #     return f"Equip({self.control_method_equipment.equipment_id}) CtrlM({
    #     self.control_method_equipment.control_method_id}) - Param '{self.parameter.abbreviation}'"


class Parameter(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'parameters'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    abbreviation = db.Column(db.String, nullable=False)
    long_name = db.Column(db.String)
    unit = db.Column(db.String)
    decimal_places = db.Column(db.Integer)
    active = db.Column(db.Boolean)
    inactivity_reason = db.Column(db.String)
    inactivity_date = db.Column(db.DateTime(timezone=True))
    # control_methods = db.relationship(
    #     'ControlMethod', secondary='control_methods_parameters', back_populates='parameters')
    # control_rules = db.relationship(
    #     'ControlRule', secondary='control_rules_parameters', back_populates='parameters')
    # session_datas = db.relationship('SessionData', back_populates='parameter')
    control_method_equipment_parameters = db.relationship(
        'ControlMethodEquipmentParameter', back_populates='parameter'
    )

    site_id = db.Column(db.Integer, db.ForeignKey('site.id'))  # Add foreign key
    site = db.relationship('Site')

    def __repr__(self):
        return self.abbreviation

    def to_dict(self):
        return {
            'id': self.id,
            'abbreviation': self.abbreviation,
            'long_name': self.long_name,
            'unit': self.unit,
            'decimal_places': self.decimal_places,
            'active': self.active,
            'inactivity_reason': self.inactivity_reason,
            'inactivity_date': self.inactivity_date
        }


class ControlMethodEquipmentParameterControlRule(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'control_methods_equipments_parameters_control_rules'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    control_method_equipment_parameter_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_methods_equipments_parameters.id'
        )
    )
    control_rule_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_rules.id'
        )
    )
    value = db.Column(db.Float)

    # parameter = db.relationship(
    #     'Parameter', back_populates='control_method_equipment_parameters')
    control_method_equipment_parameter = db.relationship(
        'ControlMethodEquipmentParameter', back_populates='control_method_equipment_parameter_controlrule'
    )
    control_rule = db.relationship(
        'ControlRule', back_populates='control_method_equipment_parameter'
    )


class ControlRule(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'control_rules'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    name = db.Column(db.String, nullable=False)
    description = db.Column(db.Text)
    control_method_equipment_parameter = db.relationship(
        'ControlMethodEquipmentParameterControlRule', back_populates='control_rule'
    )
    alarm_control_rules = db.relationship(
        'AlarmControlRule', back_populates='control_rule'
    )

    def __repr__(self):
        return self.name

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'control_method_equipment_parameters': [
                control_method_equipment_parameter.control_method_equipment_parameter.id for
                control_method_equipment_parameter in self.control_method_equipment_parameter if
                control_method_equipment_parameter.control_method_equipment_parameter is not None],
        }


class Session(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'sessions'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    control_method_equipment_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_methods_equipments.id'
        )
    )
    session_time = db.Column(db.DateTime(timezone=True))
    performed_by_id = db.Column(
        db.Integer, db.ForeignKey(
            'ab_user.id'
        ), nullable=False
    )
    # equipment = db.relationship('Equipment', back_populates='sessions')
    session_datas = db.relationship('SessionData', back_populates='session')
    performed_by = db.relationship('User', back_populates='performed_sessions')

    def __repr__(self):
        return f"{self.control_method_equipment_id} - {self.session_time}"

    def to_dict(self, control_intervals_by_parameter=None):
        session_datas = []
        if self.session_datas:
            for session_data in self.session_datas:
                param_id = session_data.control_method_equipment_parameter_id
                control_interval = None

                if control_intervals_by_parameter and param_id in control_intervals_by_parameter:
                    # Find the interval this session falls into
                    for interval in control_intervals_by_parameter[param_id]:
                        if self.id in list(interval.session_ids or []):
                            control_interval = interval  # Store interval details
                            break

                data_dict = session_data.to_dict()
                data_dict['is_in_control'] = bool(control_interval)
                if (not data_dict['sr_value'] and
                        session_data.parameter_value is not None and
                        control_interval is not None and
                        control_interval.mean_value is not None and
                        control_interval.std_value):
                    data_dict['sr_value'] = ((session_data.parameter_value - control_interval.mean_value) /
                                             control_interval.std_value)
                data_dict['alarms'] = [alarm.to_dict(include_control_rules=True) for alarm in session_data.alarms]

                session_datas.append(data_dict)
        else:
            session_datas = [{
                "id": None,
                "session_id": self.id,
                "control_method_equipment_parameter_id": None,
                "parameter_value": None,
                "open": True,
                "closed_by_id": None,
                "closed_by": None,
                "created": None,
                "notes": None,
                "is_in_control": False
            }]

        return {
            'id': self.id,
            'control_method_equipment_id': self.control_method_equipment_id,
            'session_time': self.session_time,
            "session_datas": session_datas,
            "performed_by": self.performed_by.name,
            "performed_by_id": self.performed_by_id,
        }


class SessionData(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'session_data'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    session_id = db.Column(
        db.Integer, db.ForeignKey(
            'sessions.id'
        ), nullable=False
    )
    control_method_equipment_parameter_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_methods_equipments_parameters.id'
        ), nullable=False
    )
    parameter_value = db.Column(db.Float)
    sr_value = db.Column(db.Float, nullable=True)
    session = db.relationship('Session', back_populates='session_datas')
    control_method_equipment_parameter = db.relationship(
        'ControlMethodEquipmentParameter', back_populates='session_datas'
    )
    alarms = db.relationship('Alarm', back_populates='session_data')
    open = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text, default="")
    closed_by_id = db.Column(db.Integer, db.ForeignKey('ab_user.id'))
    closed_by = db.relationship('User')

    def __repr__(self):
        return f"{self.session_id} - {self.control_method_equipment_parameter_id} - {self.parameter_value}"

    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'control_method_equipment_parameter_id': self.control_method_equipment_parameter_id,
            'parameter_value': self.parameter_value,
            'open': self.open,
            'closed_by_id': self.closed_by_id,
            'closed_by': self.closed_by.name if self.closed_by else None,
            'created': self.created,
            'notes': self.notes,
            'sr_value': self.sr_value,
        }


class Alarm(db.Model, Timestamp, AuditableMixin):
    __tablename__ = 'alarms'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    session_data_id = db.Column(
        db.Integer, db.ForeignKey(
            'session_data.id'
        ), nullable=False
    )
    open = db.Column(db.Boolean, nullable=False)
    notes = db.Column(db.Text)
    closed_by_id = db.Column(db.Integer, db.ForeignKey('ab_user.id'))

    session_data = db.relationship('SessionData', back_populates='alarms')
    closed_by = db.relationship('User', back_populates='alarms_closed_by_user')
    alarm_control_rules = db.relationship(
        'AlarmControlRule',
        back_populates='alarm',
        cascade="all, delete-orphan",
        passive_deletes=True
    )

    def __repr__(self):
        return f"{self.session_data_id} - {self.notes if self.notes is not None else ''}"
        # return f"{self.session_data_id} - {self.relevant_control_rule_id} - {self.notes if self.notes is not None
        # else ''}"

    def to_dict(self, include_control_rules=False):
        dict = {
            'id': self.id,
            'session_data_id': self.session_data_id,
            'open': self.open,
            'notes': self.notes,
            'closed_by_id': self.closed_by_id,
            'relevant_control_rule_ids': [acr.control_rule_id for acr in self.alarm_control_rules],
            'created': self.created,
        }
        if include_control_rules:
            dict['relevant_control_rules'] = [acr.control_rule.to_dict()
                                              for acr in self.alarm_control_rules]
        return dict


class AlarmControlRule(db.Model, AuditableMixin):
    __tablename__ = 'alarm_control_rules'
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    alarm_id = db.Column(
        db.Integer, db.ForeignKey(
            'alarms.id', ondelete='CASCADE'
        ), nullable=False
    )
    control_rule_id = db.Column(
        db.Integer, db.ForeignKey(
            'control_rules.id'
        ), nullable=False
    )

    alarm = db.relationship('Alarm', back_populates='alarm_control_rules', passive_deletes=True)
    control_rule = db.relationship(
        'ControlRule', back_populates='alarm_control_rules', passive_updates=True
    )

    def __repr__(self):
        return f"{self.alarm_id} - {self.control_rule_id}"

    def to_dict(self):
        return {
            'id': self.id,
            'alarm_id': self.alarm_id,
            'control_rule_id': self.control_rule_id,
        }
