import logging
import random
from datetime import datetime

from app import db
from app.auth.models import User, Site
from app.qc.models import (
    Lab, Equipment, ControlMethod, Parameter, Session, SessionData, Alarm,
    ControlRuleInterval, ControlRule, ControlMethodEquipment,
    ControlMethodEquipmentParameter, ControlMethodEquipmentParameterControlRule,
    EquipmentCompany, AlarmControlRule
)
from app.qc.test_data import (
    LAB_DATA, EQUIPMENT_DATA, CONTROL_METHOD_DATA, PARAMETERS_40_DATA, PARAMETERS_41_DATA,
    SESSIONS_40_DATA, SESSIONS_41_DATA, ALARMS_40_DATA, ALARMS_41_DATA,
    LATEST_PARAMETER_CONTROL_INTERVALS_40_DATA, LATEST_PARAMETER_CONTROL_INTERVALS_41_DATA,
    CONTROL_RULE_INTERVALS_40_DATA, CONTROL_RULE_INTERVALS_41_DATA, CONTROL_RULES_DATA
)

logger = logging.getLogger(__name__)


def parse_date(date_string):
    if not date_string:
        return None
    for fmt in ('%Y-%m-%dT%H:%M:%S.%f', '%a, %d %b %Y %H:%M:%S %Z'):
        try:
            return datetime.strptime(date_string, fmt)
        except (ValueError, TypeError):
            continue
    raise ValueError(f"could not parse date: {date_string}")


def seed_qc_data(site_id: int):
    """
    Fills up the QC models with test data for a specific site.
    This function is designed to be idempotent. It does not use hardcoded IDs
    from dummy data, letting SQLAlchemy handle ID generation.
    """
    # Mappings from old dummy IDs to new SQLAlchemy objects
    lab_map = {}
    equipment_map = {}
    parameter_map = {}
    control_method_map = {}
    cm_equipment_map = {}
    cmep_map = {}
    control_rule_map = {}
    session_map = {}
    session_data_map = {}
    alarm_map = {}
    interval_map = {}

    # --- Site and User validation ---
    site_users = User.query.join(User.sites).filter(Site.id == site_id).all()

    # --- Seeding Logic ---

    # Labs
    for lab_info in LAB_DATA:
        lab = Lab(name=lab_info['name'], site__ref_id=site_id)
        db.session.add(lab)
        db.session.flush()
        lab_map[lab.id] = lab

    # Equipment
    eq_data = EQUIPMENT_DATA.copy()
    equipment = Equipment(
        model=eq_data['model'],
        serial_no=eq_data['serial_no'],
        purchase_date=eq_data['purchase_date'],
        active=eq_data['active'],
        inactivity_date=parse_date(eq_data['inactivity_date']),
        inactivity_reason=eq_data['inactivity_reason'],
        lab_id=list(lab_map.values())[0].id
    )
    db.session.add(equipment)
    db.session.flush()
    equipment_map[equipment.id] = equipment

    # Parameters
    all_parameters = PARAMETERS_40_DATA + PARAMETERS_41_DATA
    for param_info in all_parameters:
        param = Parameter.query.filter_by(abbreviation=param_info['abbreviation'], site_id=site_id).first()
        if not param:
            param = Parameter(
                abbreviation=param_info['abbreviation'],
                long_name=param_info['long_name'],
                unit=param_info['unit'],
                decimal_places=param_info['decimal_places'],
                active=param_info['active'],
                inactivity_date=parse_date(param_info.get('inactivity_date')),
                inactivity_reason=param_info.get('inactivity_reason'),
                site_id=site_id
            )
            db.session.add(param)
            db.session.flush()
        parameter_map[param_info['id']] = param

    # Control Methods
    for cm_info in CONTROL_METHOD_DATA:
        cm = ControlMethod(
            control_method_type=cm_info['control_method_type'],
            first_name=cm_info['first_name'],
            sur_name=cm_info['sur_name'],
            gender=cm_info['gender'],
            date_of_birth=parse_date(cm_info['date_of_birth'])
        )
        db.session.add(cm)
        db.session.flush()
        control_method_map[cm_info['id']] = cm

        # ControlMethodEquipment
        cme = ControlMethodEquipment(
            control_method_id=cm.id,
            equipment_id=equipment.id
        )
        db.session.add(cme)
        db.session.flush()
        cm_equipment_map[cm_info['control_methods_equipments_id']] = cme

    # Control Method Equipment Parameters
    for param_info in PARAMETERS_40_DATA:
        cmep = ControlMethodEquipmentParameter(
            control_method_equipment_id=cm_equipment_map[40].id,
            parameter_id=parameter_map[param_info['id']].id
        )
        db.session.add(cmep)
        db.session.flush()
        cmep_map[param_info['control_methods_equipments_parameter_id']] = cmep

    for param_info in PARAMETERS_41_DATA:
        cmep = ControlMethodEquipmentParameter(
            control_method_equipment_id=cm_equipment_map[41].id,
            parameter_id=parameter_map[param_info['id']].id
        )
        db.session.add(cmep)
        db.session.flush()
        cmep_map[param_info['control_methods_equipments_parameter_id']] = cmep

    # Control Rules
    for rule_info in CONTROL_RULES_DATA:
        rule = ControlRule.query.filter_by(name=rule_info['name']).first()
        control_rule_map[rule.id] = rule

    # Sessions and Session Data
    all_sessions = SESSIONS_40_DATA + SESSIONS_41_DATA
    for session_info in all_sessions:
        session = Session(
            control_method_equipment_id=cm_equipment_map[session_info['control_method_equipment_id']].id,
            session_time=parse_date(session_info['session_time']),
            performed_by_id=random.choice(site_users).id
        )
        db.session.add(session)
        db.session.flush()
        session_map[session_info['id']] = session

        for sd_info in session_info['session_datas']:
            closed_by_id = random.choice(site_users).id if sd_info.get('closed_by_id') else None
            sd = SessionData(
                session_id=session.id,
                control_method_equipment_parameter_id=cmep_map[sd_info['control_method_equipment_parameter_id']].id,
                parameter_value=sd_info['parameter_value'],
                sr_value=sd_info['sr_value'],
                open=sd_info['open'],
                notes=sd_info['notes'],
                closed_by_id=closed_by_id
            )
            db.session.add(sd)
            db.session.flush()
            session_data_map[sd_info['id']] = sd

    # Control Rule Intervals
    all_intervals = CONTROL_RULE_INTERVALS_40_DATA + CONTROL_RULE_INTERVALS_41_DATA
    for interval_info in all_intervals:
        sessions = [session_map[x].id for x in interval_info['session_ids']]
        interval = ControlRuleInterval(
            control_method_equipment_parameter_id=cmep_map[interval_info['control_method_equipment_parameter_id']].id,
            created=parse_date(interval_info['created']),
            session_ids=sessions,
            values_count=len(sessions)
        )
        db.session.add(interval)
        db.session.flush()
        interval_map[interval_info['id']] = interval

    # Alarms and AlarmControlRules
    all_alarms = ALARMS_40_DATA + ALARMS_41_DATA
    for alarm_info in all_alarms:
        closed_by_id = random.choice(site_users).id if alarm_info.get('closed_by_id') else None
        alarm = Alarm(
            session_data_id=session_data_map[alarm_info['session_data_id']].id,
            open=alarm_info['open'],
            notes=alarm_info['notes'],
            closed_by_id=closed_by_id,
        )
        db.session.add(alarm)
        db.session.flush()
        alarm_map[alarm_info['id']] = alarm

        for rule_id in alarm_info['relevant_control_rule_ids']:
            acr = AlarmControlRule(alarm_id=alarm.id, control_rule_id=control_rule_map[rule_id].id)
            db.session.add(acr)

    # Update ControlRuleIntervals with stats and create ControlMethodEquipmentParameterControlRule
    all_latest_intervals = (LATEST_PARAMETER_CONTROL_INTERVALS_40_DATA['data']['control_methods_equipments_parameters'] +
                            LATEST_PARAMETER_CONTROL_INTERVALS_41_DATA['data']['control_methods_equipments_parameters'])
    for latest_interval_info in all_latest_intervals:
        for rule in latest_interval_info['control_methods_equipments_parameters_control_rules']:
            cmep_id = cmep_map[latest_interval_info['id']].id
            cr_id = control_rule_map[rule['control_rule']['id']].id
            existing = ControlMethodEquipmentParameterControlRule.query.filter_by(
                control_method_equipment_parameter_id=cmep_id,
                control_rule_id=cr_id
            ).first()

            if not existing:
                cmepcr = ControlMethodEquipmentParameterControlRule(
                    control_method_equipment_parameter_id=cmep_id,
                    control_rule_id=cr_id,
                    value=rule['value']
                )
                db.session.add(cmepcr)

    db.session.commit()

    for latest_interval_info in all_latest_intervals:
        for node in latest_interval_info['control_rule_intervals_aggregate']['nodes']:
            interval = interval_map[node['id']]
            interval.calculate_statistics()
            db.session.add(interval)
    db.session.commit()
    logger.info(f"Finished QC data seeding for site_id: {site_id}.") 