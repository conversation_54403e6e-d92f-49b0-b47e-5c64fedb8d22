from datetime import datetime
import logging

from flask import render_template, Blueprint, jsonify, request, session as user_session
from flask_jwt_extended import (
    jwt_required,
    get_jwt_identity
)
from flask_login import login_required, current_user

from app.auth.token import get_current_site_id

logger = logging.getLogger(__name__)
from sqlalchemy import func, asc, exists

from app import db, csrf
from app.auth.decorator import hybrid_auth_required
from app.auth.models import User, UserSiteControl
from app.qc.models import (
    AlarmControlRule, ControlMethodEquipment, ControlMethodEquipmentParameterControlRule,
    ControlRuleInterval, Lab, Equipment, Parameter, ControlMethod, ControlRule,
    Session, SessionData, Alarm, ControlMethodEquipmentParameter
)
from app.utils.models import Site

qc_bp = Blueprint(
    "qc_bp", __name__,
    template_folder="templates", static_folder="static"
)


@login_required
@qc_bp.route('/config')
def config_route():
    return render_template('config.html')


@qc_bp.route('/ps')
def ps_route():
    return render_template('ps.html')


@qc_bp.route('/devreport')
def devreport_route():
    return render_template('devreport.html')


@login_required
@qc_bp.route('/parameters')
def parameters():
    return render_template('parameters.html')


@login_required
@qc_bp.route('/equipments')
def equipments():
    return render_template('equipments.html')


@login_required
@qc_bp.route('/alerts')
def alerts_page():
    # return jsonify({"message": "Alerts page"})
    return render_template('alerts.html')


@qc_bp.route('/api/qc/site/list')
@hybrid_auth_required()
def api_site_list(authenticated_user):
    if authenticated_user.is_dev_user:
        return jsonify([dict(value=site.id, text=site.name) for site in Site.query.all()]), 200
    else:
        return jsonify([dict(value=site.id, text=site.name) for site in authenticated_user.sites]), 200


@qc_bp.route('/api/qc/site/set_site')
@hybrid_auth_required()
def api_site_set(authenticated_user):
    if request.args.get('site_id') and request.args.get('site_id').isnumeric():
        if authenticated_user.is_dev_user:
            site = Site.query.get(int(request.args.get('site_id')))
            user_session['site_id'] = site.id
            user_session['site_text'] = site.name
        else:
            site = Site.query.get(int(request.args.get('site_id')))
            user_session['site_id'] = site.id
            user_session['site_text'] = site.name
        return jsonify({"message": "Site set successfully"}), 200
    return jsonify({"error": "Invalid site_id"}), 400


@qc_bp.route('/api/qc/labs', methods=['GET'])
@hybrid_auth_required()
def get_all_labs(authenticated_user):
    site_id = get_current_site_id()
    user_Sites = [site.id for site in authenticated_user.sites]

    if not authenticated_user.is_dev_user and site_id not in user_Sites:
        return jsonify({"error": "Site not found"}), 404

    try:
        labs = Lab.query.filter_by(site__ref_id=site_id).all()
        if not labs:
            return jsonify({"error": "No labs found for the site"}), 404
        return jsonify([lab.to_dict() for lab in labs]), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@qc_bp.route('/api/qc/equipments', methods=['GET'])
@hybrid_auth_required()
def get_equipments(authenticated_user):
    lab_id = request.args.get('lab_id', type=int)
    if lab_id:
        equipments = Equipment.query.filter_by(lab_id=lab_id).all()
    else:
        equipments = Equipment.query.all()
    return jsonify([equipment.to_dict() for equipment in equipments]), 200


@qc_bp.route('/api/qc/control-rules', methods=['GET'])
def get_control_rules():
    try:
        control_methods_equipments_id = request.args.get('control_methods_equipments_id', type=int)
        query = ControlRule.query
        if control_methods_equipments_id:
            # query = ControlRule.query
            query = db.session.query(ControlRule, ControlMethodEquipmentParameterControlRule) \
                .select_from(ControlRule) \
                .join(
                ControlMethodEquipmentParameterControlRule,
                ControlMethodEquipmentParameterControlRule.control_rule_id == ControlRule.id
            ) \
                .join(
                ControlMethodEquipmentParameter,
                ControlMethodEquipmentParameter.id ==
                ControlMethodEquipmentParameterControlRule.control_method_equipment_parameter_id
            ) \
                .filter(ControlMethodEquipmentParameter.control_method_equipment_id == control_methods_equipments_id)
            # query = db.session.query(ControlMethodEquipmentParameterControlRule)
        else:
            # return jsonify({"error": "control_methods_equipments_id is required"}), 400
            q = query.all()
            return jsonify([c.to_dict() for c in q])
        rows = query.all()
        rules = []
        for r in rows:
            d = r.ControlRule.to_dict()
            d['value'] = r.ControlMethodEquipmentParameterControlRule.value
            rules.append(d)
        return jsonify(rules)

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@qc_bp.route('/api/qc/control-rules-test', methods=['GET'])
def get_control_rules_for_parameter():
    control_method_equipment_parameter_id = request.args.get(
        'control_method_equipment_parameter_id'
    )
    if not control_method_equipment_parameter_id:
        return jsonify({"error": "control_method_equipment_parameter_id is required"}), 400

    control_rules = db.session.query(ControlRule, ControlMethodEquipmentParameterControlRule) \
        .select_from(ControlRule) \
        .join(
        ControlMethodEquipmentParameterControlRule,
        ControlMethodEquipmentParameterControlRule.control_rule_id == ControlRule.id
    ) \
        .filter(
        ControlMethodEquipmentParameterControlRule.control_method_equipment_parameter_id ==
        control_method_equipment_parameter_id
    ) \
        .all()

    control_rules_data = []
    for row in control_rules:
        control_rules_data.append(
            {
                "id": row.ControlRule.id,
                "name": row.ControlRule.name,
                "description": row.ControlRule.description,
                "value": row.ControlMethodEquipmentParameterControlRule.value
            }
        )

    return jsonify(control_rules_data), 200


@qc_bp.route('/api/qc/control-rule-intervals', methods=['GET'])
def get_control_rule_intervals():
    control_methods_equipments_id = request.args.get(
        'control_methods_equipments_id', type=int
    )
    query = ControlRuleInterval.query
    if control_methods_equipments_id:
        query = db.session.query(ControlRuleInterval).join(
            ControlMethodEquipmentParameter,
            ControlMethodEquipmentParameter.id == ControlRuleInterval.control_method_equipment_parameter_id
        ) \
            .filter(ControlMethodEquipmentParameter.control_method_equipment_id == control_methods_equipments_id)
    intervals = query.all()

    return jsonify([interval.to_dict() for interval in intervals])


@csrf.exempt
@qc_bp.route('/api/qc/control-rule-intervals', methods=['POST'])
def create_control_rule_interval():
    data = request.json

    control_method_equipment_parameter_id = data.get(
        'control_method_equipment_parameter_id'
    )
    session_ids = data.get('session_ids')

    # Check if the required fields are present
    if not control_method_equipment_parameter_id or not session_ids:
        return jsonify(
            {"error": "control_method_equipment_parameter_id, session_ids are required"}
        ), 400

    # Check if the interval already exists
    interval = ControlRuleInterval.query.filter(
        ControlRuleInterval.control_method_equipment_parameter_id == control_method_equipment_parameter_id,
        ControlRuleInterval.session_ids.overlap(session_ids),
    ).first()

    if interval:
        return jsonify({"error": "Interval overlapping with another interval"}), 400

    new_interval = ControlRuleInterval(
        control_method_equipment_parameter_id=control_method_equipment_parameter_id,
        session_ids=session_ids,
        values_count=len(session_ids)
    )

    new_interval.calculate_statistics()

    db.session.add(new_interval)
    db.session.commit()

    sessions_alerts = Alarm.query.join(SessionData).filter(
        SessionData.session_id.in_(session_ids),
        SessionData.control_method_equipment_parameter_id == control_method_equipment_parameter_id
    ).all()

    for session_alert in sessions_alerts:
        db.session.delete(session_alert)
    db.session.commit()

    return jsonify(new_interval.to_dict()), 201


@qc_bp.route('/api/qc/control-methods', methods=['GET'])
def get_control_methods():
    equipment_id = request.args.get('equipment_id')
    parameter_id = request.args.get('parameter_id')

    if equipment_id:
        methods = db.session.query(ControlMethod, ControlMethodEquipment).join(ControlMethodEquipment).filter(
            ControlMethodEquipment.equipment_id == equipment_id
        ).all()

        dicts = []
        for method, control_method_equipment in methods:
            dicts.append(method.to_dict())
            dicts[-1]['control_methods_equipments_id'] = control_method_equipment.id
        methods = dicts
    elif parameter_id:
        methods = db.session.query(ControlMethod).join(ControlMethodEquipment).join(
            ControlMethodEquipmentParameter
        ).filter(
            ControlMethodEquipmentParameter.parameter_id == parameter_id
        ).all()

        methods = [method.to_dict() for method in methods]
    else:
        methods = ControlMethod.query.all()
        methods = [method.to_dict() for method in methods]

    return jsonify(methods), 200


@qc_bp.route('/api/qc/parameters', methods=['GET'])
def get_parameters():
    control_methods_equipments_id = request.args.get(
        'control_methods_equipments_id'
    )
    lab_id = request.args.get('lab_id')

    if control_methods_equipments_id:
        parameters = db.session.query(Parameter, ControlMethodEquipmentParameter).join(
            ControlMethodEquipmentParameter
        ).filter(
            ControlMethodEquipmentParameter.control_method_equipment_id == control_methods_equipments_id
        ).all()
        dicts = []
        for parameter, control_method_equipment_parameter in parameters:
            dicts.append(parameter.to_dict())
            dicts[-1]['control_methods_equipments_parameter_id'] = control_method_equipment_parameter.id
        parameters = dicts
    elif lab_id:
        parameters = db.session.query(Parameter).filter(
            Parameter.id == lab_id
        ).all()
        parameters = [parameter.to_dict() for parameter in parameters]
    else:
        parameters = Parameter.query.all()
        parameters = [parameter.to_dict() for parameter in parameters]

    return jsonify(parameters), 200


@qc_bp.route('/api/qc/equipments/filter', methods=['GET'])
def get_filtered_equipments():
    lab_id = request.args.get('lab_id', type=int)
    parameter_id = request.args.get('parameter_id', type=int)
    control_method_id = request.args.get('control_method_id', type=int)

    if not lab_id or not parameter_id or not control_method_id:
        return jsonify({"error": "lab_id, parameter_id, and control_method_id are required"}), 400

    query = db.session.query(Equipment).join(ControlMethodEquipment).join(ControlMethodEquipmentParameter) \
        .filter(
        Equipment.lab_id == lab_id,
        ControlMethodEquipment.control_method_id == control_method_id,
        ControlMethodEquipmentParameter.parameter_id == parameter_id
    )

    equipments = query.all()

    equipment_list = [equipment.to_dict() for equipment in equipments]

    return jsonify(equipment_list), 200


@qc_bp.route('/api/qc/parameter/filter', methods=['GET'])
def get_filtered_equip():
    lab_id = request.args.get('lab_id', type=int)
    query = (
        db.session.query(Parameter)
        .join(ControlMethodEquipmentParameter, Parameter.id == ControlMethodEquipmentParameter.parameter_id)
        .join(
            ControlMethodEquipment,
            ControlMethodEquipmentParameter.control_method_equipment_id == ControlMethodEquipment.id
        )
        .join(Equipment, ControlMethodEquipment.equipment_id == Equipment.id)
        .join(Lab, Equipment.lab_id == Lab.id)
        .filter(
            lab_id == Lab.id
        )
        .all()
    )

    parameter_list = [ele.to_dict() for ele in query]
    return jsonify(parameter_list), 200


@qc_bp.route('/api/qc/control-method/filter', methods=['GET'])
def get_filtered_methods():
    parameter_id = request.args.get('parameter_id', type=int)
    query = db.session.query(ControlMethod) \
        .join(ControlMethodEquipment, ControlMethod.id == ControlMethodEquipment.control_method_id) \
        .join(
        ControlMethodEquipmentParameter,
        ControlMethodEquipment.id == ControlMethodEquipmentParameter.control_method_equipment_id
    ) \
        .join(Parameter, Parameter.id == ControlMethodEquipmentParameter.parameter_id) \
        .filter(Parameter.id == parameter_id) \
        .all()

    controlMethods_list = [ele.to_dict() for ele in query]
    return jsonify(controlMethods_list), 200


@qc_bp.route('/api/qc/sessions', methods=['POST'])
@jwt_required()
def add_session():
    current_user_id = get_jwt_identity()
    # Get the data from the request
    data = request.json
    # Create a new session object
    # get the user id from the session
    # user_id = 1
    session = Session(
        control_method_equipment_id=data['control_method_equipment_id'],
        session_time=datetime.fromisoformat(data["session_time"].replace("Z", "+00:00")),
        performed_by_id=data['performed_by'] or current_user_id
    )

    for param_id, value in (data['parameters'] or {}).items():
        if value is None:
            continue

        session_data = SessionData.query.filter_by(
            session_id=session.id,
            control_method_equipment_parameter_id=param_id
        ).first()
        # if the session_data exists, update the value
        if session_data:
            session_data.parameter_value = float(value) if value is not None else 0.0
        else:
            # create a new session data object
            session_data = SessionData(
                session_id=session.id,
                control_method_equipment_parameter_id=param_id,
                parameter_value=float(value) if value is not None else 0.0
            )
            # add the session data to the session
            session.session_datas.append(session_data)

    # Add the session to the database
    db.session.add(session)
    db.session.commit()

    logger.debug("added_session")
    return jsonify(session.to_dict()), 201


@qc_bp.route('/api/qc/sessions', methods=['GET'])
@jwt_required()
def get_sessions():
    # Get the data from the request
    control_method_equipment_id = request.args.get('control_method_equipment_id')
    if not control_method_equipment_id:
        return jsonify({"error": "control_method_equipment_id is required"}), 400

    sessions = Session.query.filter_by(
        control_method_equipment_id=control_method_equipment_id
    ).options(
        db.joinedload(Session.session_datas).joinedload(SessionData.alarms).joinedload(Alarm.alarm_control_rules)
    ).order_by(Session.session_time.asc()).all()

    # Get all unique parameter IDs from all sessions
    parameter_ids = set()
    for session in sessions:
        for session_data in session.session_datas:
            if session_data.control_method_equipment_parameter_id:
                parameter_ids.add(session_data.control_method_equipment_parameter_id)

    # Fetch all relevant control intervals at once
    control_intervals_by_parameter = {}
    if parameter_ids:
        all_intervals = ControlRuleInterval.query.filter(
            ControlRuleInterval.control_method_equipment_parameter_id.in_(list(parameter_ids))
        ).all()

        # Group intervals by parameter_id for faster lookup
        for interval in all_intervals:
            param_id = interval.control_method_equipment_parameter_id
            if param_id not in control_intervals_by_parameter:
                control_intervals_by_parameter[param_id] = []
            control_intervals_by_parameter[param_id].append(interval)

    return jsonify([session.to_dict(control_intervals_by_parameter) for session in sessions]), 200


# update session


@qc_bp.route('/api/qc/sessions/<int:session_id>/notes', methods=['PATCH'])
@jwt_required()
def update_session_notes(session_id):
    # update sessionDatas notes,open, closed_by_id for the session
    try:

        data = request.json
        control_method_equipment_id = data.get('control_method_equipment_id')

        session = Session.query.filter_by(
            id=session_id, control_method_equipment_id=control_method_equipment_id
        ).first()

        if not session:
            return jsonify({"error": "Session not found"}), 404

        if 'session_time' in data:
            session.session_time = data['session_time']
            db.session.commit()

        if 'performed_by_id' in data:
            session.performed_by_id = data['performed_by_id']
            db.session.commit()

        session_datas: list = SessionData.query.filter_by(
            session_id=session_id
        ).all()
        for session_data in session_datas:
            session_data.notes = data['notes']
            session_data.open = data['open'] if 'open' in data else session_data.open
            session_data.closed_by_id = int(
                data['closed_by_id']
            ) if 'closed_by_id' in data else session_data.closed_by_id
            db.session.commit()
        if 'open' in data:
            alarms = Alarm.query.join(SessionData, SessionData.id == Alarm.session_data_id) \
                .join(Session, Session.id == SessionData.session_id) \
                .filter(Session.id == session_id).all()
            for alarm in alarms:
                alarm.open = data['open']
                db.session.commit()

        return jsonify(session.to_dict()), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@qc_bp.route('/api/qc/alarms', methods=['GET'])
@jwt_required()
def get_alarms():
    control_method_equipment_id = request.args.get(
        'control_method_equipment_id'
    )
    if control_method_equipment_id:
        alarms = (db.session.query(Alarm)
                  .join(SessionData, SessionData.id == Alarm.session_data_id)
                  .join(Session, Session.id == SessionData.session_id)
                  .filter(Session.control_method_equipment_id == control_method_equipment_id)
                  .order_by(Alarm.id.asc())
                  .order_by(Alarm.open.desc())
                  .all())
    else:
        alarms = Alarm.query.all()
    return jsonify([alarm.to_dict() for alarm in alarms]), 200


@qc_bp.route('/api/qc/sessions/<int:session_id>', methods=['PATCH'])
@jwt_required()
def update_session_data(session_id):
    # get the parameter_id and value from the request
    data = request.json
    # get the session
    session = Session.query.get(session_id)
    # check if the session_data already exists
    session_data = SessionData.query.filter_by(
        session_id=session_id, control_method_equipment_parameter_id=data['control_methods_equipments_parameter_id']
    ).first()
    # if the session_data exists, update the value
    if session_data:
        session_data.parameter_value = float(data['value'])
        db.session.add(session_data)
        db.session.commit()
    else:
        # create a new session data object
        session_data = SessionData(
            session_id=session_id,
            # parameter_id=data['parameter_id'],
            control_method_equipment_parameter_id=data['control_methods_equipments_parameter_id'],
            parameter_value=float(data['value'])
        )
        # add the session data to the session
        session.session_datas.append(session_data)
        db.session.commit()
    return jsonify({}), 201


@qc_bp.route('/api/qc/sessions/<int:session_id>', methods=['DELETE'])
@jwt_required()
def delete_session(session_id):
    try:
        control_method_equipment_id = request.args.get(
            'control_method_equipment_id'
        )
        session = Session.query.filter_by(
            id=session_id, control_method_equipment_id=control_method_equipment_id
        ).first()

        if not session:
            return jsonify({"error": "Session not found"}), 404

        control_rule_intervals = ControlRuleInterval.query.filter(
            ControlRuleInterval.session_ids.contains([session_id])
        ).all()
        if control_rule_intervals:
            return jsonify({"error": "You cannot delete a session inside a control interval"}), 400

        session_datas = SessionData.query.filter_by(
            session_id=session_id
        ).all()
        alarms = Alarm.query.join(SessionData, SessionData.id == Alarm.session_data_id) \
            .join(Session, Session.id == SessionData.session_id) \
            .filter(Session.id == session_id).all()

        alarm_control_rules = AlarmControlRule.query.join(Alarm, Alarm.id == AlarmControlRule.alarm_id) \
            .join(SessionData, SessionData.id == Alarm.session_data_id) \
            .join(Session, Session.id == SessionData.session_id) \
            .filter(Session.id == session_id).all()

        if alarm_control_rules:
            for alarm_control_rule in alarm_control_rules:
                db.session.delete(alarm_control_rule)

        for alarm in alarms:
            db.session.delete(alarm)

        for session_data in session_datas:
            db.session.delete(session_data)

        db.session.delete(session)
        db.session.commit()

        return jsonify({"message": "Session and related data deleted"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@qc_bp.route('/api/qc/session_datas')
def get_session_datas():
    lab_id = request.args.get('lab_id', type=int)
    control_method_id = request.args.get('control_method_id', type=int)
    parameter_id = request.args.get('parameter_id', type=int)

    # query = db.session.query(SessionData, Equipment.id.label('equipment_id'), Session.session_time) \
    #     .join(Equipment, Equipment.lab_id == lab_id and ControlMethodEquipment.equipment_id == Equipment.id) \
    #     .join(ControlMethodEquipment, ControlMethodEquipment.id == control_method_id and Equipment.id ==
    #     ControlMethodEquipment.equipment_id) \
    #     .join(ControlMethodEquipmentParameter, ControlMethodEquipmentParameter.control_method_equipment_id ==
    #     ControlMethodEquipment.id and ControlMethodEquipmentParameter.parameter_id == parameter_id) \
    #     .join(Session, Session.control_method_equipment_id == ControlMethodEquipment.id and Session.id ==
    #     SessionData.session_id) \
    #     .filter(SessionData.control_method_equipment_parameter_id == ControlMethodEquipmentParameter.id
    #             and Equipment.lab_id == lab_id
    #             )

    # query = db.session.query(SessionData, Equipment.id.label('equipment_id'), Session.session_time) \
    #     .select_from(SessionData) \
    #     .join(Session, Session.id == SessionData.session_id) \
    #     .join(ControlMethodEquipmentParameter, ControlMethodEquipmentParameter.id ==
    #     SessionData.control_method_equipment_parameter_id) \
    #     .join(ControlMethodEquipment, ControlMethodEquipment.id ==
    #     ControlMethodEquipmentParameter.control_method_equipment_id) \
    #     .join(Equipment, Equipment.id == ControlMethodEquipment.equipment_id) \
    #     .filter(ControlMethodEquipment.id == control_method_id,
    #             ControlMethodEquipmentParameter.parameter_id == parameter_id,
    #             Equipment.lab_id == lab_id)

    query = db.session.query(
        SessionData, Equipment.id.label('equipment_id'), Session.session_time, Alarm,
        ControlRule
    ) \
        .select_from(SessionData) \
        .join(Session, Session.id == SessionData.session_id) \
        .join(
        ControlMethodEquipmentParameter,
        ControlMethodEquipmentParameter.id == SessionData.control_method_equipment_parameter_id
    ) \
        .join(
        ControlMethodEquipment, ControlMethodEquipment.id == ControlMethodEquipmentParameter.control_method_equipment_id
    ) \
        .join(Equipment, Equipment.id == ControlMethodEquipment.equipment_id) \
        .outerjoin(Alarm, Alarm.session_data_id == SessionData.id) \
        .outerjoin(AlarmControlRule, AlarmControlRule.alarm_id == Alarm.id) \
        .outerjoin(ControlRule, ControlRule.id == AlarmControlRule.control_rule_id) \
        .filter(
        ControlMethodEquipment.control_method_id == control_method_id,
        ControlMethodEquipmentParameter.parameter_id == parameter_id,
        Equipment.lab_id == lab_id
    ) \
        .order_by(asc(Session.session_time))

    rows = query.all()
    session_datas = []
    for r in rows:
        if any(r.SessionData.id == sd['id'] for sd in session_datas):
            if not r.Alarm or not r.ControlRule:
                continue
            for i, sd in enumerate(session_datas):
                if sd['id'] == r.SessionData.id:
                    session_datas[i]['alarm']['control_rules'].append(
                        r.ControlRule.to_dict()
                    )
                    break
            continue
        session_datas.append(r.SessionData.to_dict())
        session_datas[-1]['equipment_id'] = r.equipment_id
        session_datas[-1]['session_time'] = r.session_time
        if r.Alarm:
            session_datas[-1]['alarm'] = r.Alarm.to_dict()
            session_datas[-1]['alarm']['control_rules'] = []
            # If a control rule exists, append it to the control_rules array
            if r.ControlRule:
                session_datas[-1]['alarm']['control_rules'].append(
                    r.ControlRule.to_dict()
                )

    return jsonify(session_datas)


@qc_bp.route('/api/qc/parameter-session-alarm-counts', methods=['GET'])
def get_parameter_session_alarm_counts():
    # parameter_id = request.args.get('parameter_id', type=int)
    equipment_id = request.args.get('equipment_id', type=int)
    start_date = request.args.get('start_date', type=str)
    end_date = request.args.get('end_date', type=str)

    try:
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
    except ValueError:
        return jsonify({"error": "Invalid date format. Use YYYY-MM-DD."}), 400

    query = db.session.query(
        func.date_trunc('month', Session.session_time).label(
            'session_month'
        ),  # Group by month
        # func.date(Session.session_time).label('session_month'),  # Group by day
        SessionData.control_method_equipment_parameter_id,
        func.count(Session.id).label('session_count'),
        func.count(Alarm.id).label('alarm_count')  #
    ) \
        .select_from(ControlMethodEquipmentParameter) \
        .join(
        ControlMethodEquipment, ControlMethodEquipmentParameter.control_method_equipment_id == ControlMethodEquipment.id
    ) \
        .join(SessionData, ControlMethodEquipmentParameter.id == SessionData.control_method_equipment_parameter_id) \
        .join(Session, SessionData.session_id == Session.id) \
        .outerjoin(Alarm, SessionData.id == Alarm.session_data_id) \
        .filter(ControlMethodEquipment.equipment_id == equipment_id)

    if start_date and end_date:
        query = query.filter(
            Session.session_time >=
            start_date, Session.session_time <= end_date
        )

    # Group by session date and control_method_equipment_parameter_id
    query = query.group_by(
        func.date_trunc(
            'month', Session.session_time
        ), SessionData.control_method_equipment_parameter_id
    )
    # query = query.group_by(func.date(Session.session_time), SessionData.control_method_equipment_parameter_id) #
    # Group by day

    results = query.all()

    # Extracting results into a list of dictionaries
    counts = [
        {
            'session_month': result.session_month,
            'control_method_equipment_parameter_id': result.control_method_equipment_parameter_id,
            'session_count': result.session_count,
            'alarm_count': result.alarm_count
        }
        for result in results
    ]

    return jsonify(counts)
    # return jsonify(counts)


@csrf.exempt
@qc_bp.route('/api/qc/control-methods-equipments', methods=['POST', "DELETE"])
def add_remove_control_method_equipment():
    data = request.json
    control_method_id = data.get('control_method_id')
    equipment_id = data.get('equipment_id')

    if not control_method_id or not equipment_id:
        return jsonify({"error": "control_method_id and equipment_id are required"}), 400

    # check if the control_method_equipment already exists
    control_method_equipment = ControlMethodEquipment.query.filter_by(
        control_method_id=control_method_id, equipment_id=equipment_id
    ).first()

    if request.method == "DELETE":
        try:
            if not control_method_equipment:
                return jsonify({"error": "Control method equipment dosen't exists"}), 400
            # delete the control_method_equipment
            db.session.delete(control_method_equipment)
            db.session.commit()
            return jsonify({"message": "Success!"}), 200
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    if control_method_equipment:
        return jsonify({"error": "Control method equipment already exists"}), 400
    control_method_equipment = ControlMethodEquipment(
        control_method_id=control_method_id,
        equipment_id=equipment_id
    )
    db.session.add(control_method_equipment)
    db.session.commit()
    control_method = db.session.query(
        ControlMethod
    ).filter_by(id=control_method_id).first()
    dict = control_method.to_dict()
    dict['control_methods_equipments_id'] = control_method_equipment.id
    return jsonify(dict), 201


@csrf.exempt
@qc_bp.route('/api/qc/control-methods-equipments-parameters', methods=['POST', "DELETE"])
def add_remove_control_method_equipment_parameter():
    data = request.json
    control_method_equipment_id = data.get("control_method_equipment_id")
    parameter_id = data.get("parameter_id")

    if not control_method_equipment_id or not parameter_id:
        return jsonify({"error": "control_method_equipment and parameter_id are required"}), 400

    control_method_equipment_parameter = ControlMethodEquipmentParameter.query.filter_by(
        control_method_equipment_id=control_method_equipment_id, parameter_id=parameter_id
    ).first()

    if request.method == "DELETE":
        if not control_method_equipment_parameter:
            return jsonify({"error": "Control method Equipment Parameter dosen't exists"}), 400
        # delete the control_method_equipment_parameter
        db.session.delete(control_method_equipment_parameter)
        db.session.commit()
        return jsonify({"message": "Success!"}), 200

    if control_method_equipment_parameter:
        return jsonify({"error": "Control method Equipment Parameter already exists"}), 400
    control_method_equipment_parameter = ControlMethodEquipmentParameter(
        parameter_id=parameter_id,
        control_method_equipment_id=control_method_equipment_id
    )
    db.session.add(control_method_equipment_parameter)
    db.session.commit()
    parameter = db.session.query(Parameter).filter_by(id=parameter_id).first()
    dict = parameter.to_dict()
    dict['control_methods_equipments_parameter_id'] = control_method_equipment_parameter.id
    return jsonify(dict), 201


@csrf.exempt
@qc_bp.route('/api/qc/control-methods-equipments-parameters-control-rules', methods=['POST', "DELETE"])
def add_remove_control_method_equipment_parameter_control_rule():
    data = request.json
    control_method_equipment_parameter_id = data.get(
        "control_method_equipment_parameter_id"
    )
    control_rule_id = data.get("control_rule_id")
    value = data.get("value")

    if not control_method_equipment_parameter_id or not control_rule_id:
        return jsonify({"error": "control_method_equipment and control_rule_id are required"}), 400

    control_method_equipment_parameter_control_rule = ControlMethodEquipmentParameterControlRule.query.filter_by(
        control_method_equipment_parameter_id=control_method_equipment_parameter_id, control_rule_id=control_rule_id
    ).first()

    if request.method == "DELETE":
        if not control_method_equipment_parameter_control_rule:
            return jsonify({"error": "Control method Equipment Parameter dosen't exists"}), 400
        db.session.delete(control_method_equipment_parameter_control_rule)
        db.session.commit()
        return jsonify({"message": "Success!"}), 200

    if control_method_equipment_parameter_control_rule:
        return jsonify({"error": "Control method Equipment Parameter already exists"}), 400

    try:
        control_method_equipment_parameter_control_rule = ControlMethodEquipmentParameterControlRule(
            control_rule_id=control_rule_id,
            control_method_equipment_parameter_id=control_method_equipment_parameter_id,
            value=value
        )
        db.session.add(control_method_equipment_parameter_control_rule)
        db.session.commit()
        return jsonify({"message": "Success!"}), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@qc_bp.route('/api/qc/alerts-count', methods=['GET'])
def get_alert_count():
    parameter_id = request.args.get('parameter_id', type=int)
    method_id = request.args.get('method_id', type=int)
    equipment_id = request.args.get('equipment_id', type=int)
    start_date = request.args.get('start_date', type=str)
    end_date = request.args.get('end_date', type=str)

    start_datetime = datetime.strptime(
        start_date, "%Y-%m-%d"
    ) if start_date else None
    end_datetime = datetime.strptime(
        end_date, "%Y-%m-%d"
    ) if end_date else None

    if parameter_id is None or method_id is None or equipment_id is None:
        return jsonify({"error": "Missing parameters"}), 400

    try:
        alerts_count = db.session.query(SessionData).join(
            ControlMethodEquipmentParameter,
            SessionData.control_method_equipment_parameter_id == ControlMethodEquipmentParameter.id
        ).join(
            ControlMethodEquipment,
            ControlMethodEquipmentParameter.control_method_equipment_id == ControlMethodEquipment.id
        ).join(
            Alarm, SessionData.id == Alarm.session_data_id
        ).join(
            Session, SessionData.session_id == Session.id
        ).filter(
            ControlMethodEquipmentParameter.parameter_id == parameter_id,
            ControlMethodEquipment.equipment_id == equipment_id,
            ControlMethodEquipment.control_method_id == method_id
        )

        if start_datetime:
            alerts_count = alerts_count.filter(
                Session.session_time >= start_datetime
            )
        if end_datetime:
            alerts_count = alerts_count.filter(
                Session.session_time <= end_datetime
            )

        alerts_count = alerts_count.count()

        return jsonify({'alert_count': alerts_count})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@qc_bp.route('/api/qc/session-statistics', methods=['GET'])
def get_session_statistics():
    parameter_id = request.args.get('parameter_id', type=int)
    method_id = request.args.get('method_id', type=int)
    equipment_id = request.args.get('equipment_id', type=int)
    start_date = request.args.get('start_date', type=str)
    end_date = request.args.get('end_date', type=str)
    include_alarms = request.args.get('include_alerts', type=bool)

    if parameter_id is None or method_id is None or equipment_id is None:
        return jsonify({"error": "Missing parameters"}), 400

    if include_alarms is None:
        include_alarms = False

    try:
        # Parse dates if provided
        start_datetime = datetime.strptime(
            start_date, "%Y-%m-%d"
        ) if start_date else None
        end_datetime = datetime.strptime(
            end_date, "%Y-%m-%d"
        ) if end_date else None

        control_method_equipment_parameter = (
            db.session.query(ControlMethodEquipmentParameter)
            .join(ControlMethodEquipment)
            .filter(
                ControlMethodEquipmentParameter.parameter_id == parameter_id,
                ControlMethodEquipment.equipment_id == equipment_id,
                ControlMethodEquipment.control_method_id == method_id
            )
            .first()
        )
        if not control_method_equipment_parameter:
            return jsonify({"error": "Control method equipment parameter not found"}), 404

        # Join with the Session table to filter by session_time instead of timestamp
        query = db.session.query(SessionData).join(Session).filter(
            SessionData.control_method_equipment_parameter_id == control_method_equipment_parameter.id
        )

        if start_datetime:
            query = query.filter(Session.session_time >= start_datetime)
        if end_datetime:
            query = query.filter(Session.session_time <= end_datetime)

        if not include_alarms:
            query = query.filter(~exists().where(SessionData.id == Alarm.session_data_id))

        sessions = query.all()

        if sessions:
            values = [
                session.parameter_value for session in sessions if session.parameter_value is not None]
            count = len(values)
            mean = sum(values) / count if count > 0 else 0
            variance = sum((x - mean) ** 2 for x in values) / \
                       count if count > 0 else 0
            standard_deviation = variance ** 0.5 if count > 0 else 0

            return jsonify(
                {
                    'standard_deviation': standard_deviation,
                    'mean': mean,
                }
            ), 200
        else:
            return jsonify({'standard_deviation': 0, 'mean': 0}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@qc_bp.route('/api/qc/update_favourite/', methods=['POST'])
@jwt_required()
def update_favourite():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    site_id = get_current_site_id()
    control_type = data.get('type')
    item_id = data.get('item_id')

    if not site_id or not item_id or not control_type:
        return jsonify({"error": "Missing required fields"}), 400

    if control_type not in ["device", "parameter"]:
        return jsonify({"error": "Invalid type. Must be 'device' or 'parameter'"}), 400

    # Check if an entry of this type already exists for the user and site
    existing_entry = UserSiteControl.query.filter_by(
        user_id=current_user_id, site_id=site_id, type=control_type
    ).first()

    if existing_entry:
        # If the same item is being toggled, remove it
        if existing_entry.item_id == item_id:
            db.session.delete(existing_entry)
            db.session.commit()
            return jsonify({"message": "Removed from favourites.", "is_favourite": False})

        # Otherwise, replace the existing favorite with the new item
        existing_entry.item_id = item_id
        existing_entry.is_favourite = True
        message = "Updated favourite item."

    else:
        # No existing entry, so create a new favorite
        new_entry = UserSiteControl(
            user_id=current_user_id,
            site_id=site_id,
            type=control_type,
            item_id=item_id,
            is_favourite=True
        )
        db.session.add(new_entry)
        message = "Added to favourites."

    db.session.commit()
    return jsonify({"message": message, "is_favourite": True})


@qc_bp.route('/api/get_favourites', methods=['GET'])
@jwt_required()
def get_favourites():
    current_user_id = get_jwt_identity()

    favourites = UserSiteControl.query.filter_by(
        user_id=current_user_id,
        site_id=get_current_site_id(),
        is_favourite=True
    ).all()

    response = {
        "device": None,
        "parameter": None
    }

    for favourite in favourites:
        if favourite.type in response and response[favourite.type] is None:
            response[favourite.type] = {
                "item_id": favourite.item_id,
                "type": favourite.type,
                "site_id": favourite.site_id,
                "is_favourite": favourite.is_favourite
            }

    return jsonify(response)
