import multiprocessing
import os

#Each worker uses about 400MB of memory. So if we're hosting
#them in the same instance as <PERSON><PERSON>, we need to allocate more
#than 1GB of RAM if there is more than one instance.
if os.environ.get("APP_TYPE", "").lower() == "prod":
    workers = 1
    threads = 4
    reload = True
else:
    workers = 1
    threads = 2
    reload = False
# Heroku exposes $PORT env variable which is a random port and is bound to 80/443 to outer network
port = os.environ.get('PORT', None)
if not port:
    port = os.environ.get("FLASK_PORT", 8000)
bind = f"0.0.0.0:{int(port)}"
worker_temp_dir = "/dev/shm"
timeout = int(os.environ.get("REQUEST_TIMEOUT", 60 *  10))
graceful_timeout = 280
wsgi_app = "wsgi:app"
max_requests = 5000
max_requests_jitter = 100
forwarded_allow_ips = "*"
loglevel = "info"
accesslog = "-"
errorlog = "-"
capture_output = True
proc_name = "rezibase-gunicorn"
limit_request_fields = 50
worker_class = "gevent"
