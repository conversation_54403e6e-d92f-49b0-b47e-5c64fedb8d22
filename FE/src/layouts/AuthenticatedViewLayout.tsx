import { Navigate} from 'react-router';

import {useGlobalStoreLoader} from "@/store/global.store.ts";
import {observer} from "mobx-react-lite";
import authStore, {AuthStatus} from "@/store/auth.store.ts";

const AuthenticatedViewLayout = observer(({children}: {children: any}) => {
  useGlobalStoreLoader(false);

  if (authStore.requireLogin) {
    return <Navigate to="/auth/login" replace />
  }

  if (authStore.requirePasswordReset) {
    return <Navigate to="/auth/reset-pass" replace />
  }

  // if (authStore.requireMfaSetup) {
  //   return <Navigate to="/auth/mfa/setup" replace />
  // }
  //
  // if (authStore.requireMfa) {
  //   return <Navigate to="/auth/mfa" replace />
  // }

  if (authStore.status === AuthStatus.LOGGED_OUT && authStore.refreshToken) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Restoring session...</p>
        </div>
      </div>
    );
  }

  return children;
});

export default AuthenticatedViewLayout;