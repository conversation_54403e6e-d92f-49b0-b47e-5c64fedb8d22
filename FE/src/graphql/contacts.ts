import {gql} from '@apollo/client';
import { graphql } from '@/graphql';

export const getDoctorsData = graphql(`
  query GetDoctors($nameFilter: String) {
    doctors(where: {_or: [{surname: {_ilike: $nameFilter}}, {forename: {_ilike: $nameFilter}}]}) {
      id
      provider_number
      gp_code
      practice_code
      practice_number
      title
      forename
      surname
      initials
      clinic_name
      consulting_location
      postal_barcode
      postal_barcode_sample
      building
      street
      suburb
      state
      post_code
      phone
      ext
      fax
      mobile
      secretary_line_1
      secretary_line_2
      email
      job_description
      salutation
      specialty
      is_referring
      external_reference
      hpi_number
      hpio_number
      edi_address
      hidden
      prevent_appearing_in_new_records
      record_source
      created_at
      created_by
      last_modified_by
    }
    doctors_aggregate(where: {_or: [{surname: {_ilike: $nameFilter}}, {forename: {_ilike: $nameFilter}}]}) {
      aggregate {
        count
      }
    }
  }
`);

export const addDoctorMutation = graphql(`
  mutation AddDoctor($doctor: doctors_insert_input!) {
    insert_doctors_one(object: $doctor) {
      id
      provider_number
      gp_code
      practice_code
      practice_number
      title
      forename
      surname
      initials
      clinic_name
      consulting_location
      postal_barcode
      postal_barcode_sample
      building
      street
      suburb
      state
      post_code
      phone
      ext
      fax
      mobile
      secretary_line_1
      secretary_line_2
      email
      job_description
      salutation
      specialty
      is_referring
      external_reference
      hpi_number
      hpio_number
      edi_address
      hidden
      prevent_appearing_in_new_records
      record_source
      created_at
      created_by
      last_modified_by
    }
  }
`);

export const mutateDoctorData = (fieldName: string, value: string | number) => {
  return gql`
    mutation ($doctorId: Int){
      update_doctors(where: {id: {_eq: $doctorId}}, _set:{${fieldName}: "${value}"}){
        affected_rows
        returning {
          id
        }
      }
    }
  `;
};