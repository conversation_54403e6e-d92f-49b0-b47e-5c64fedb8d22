import { useState } from 'react';
import PatientSearchBox from './PatientSearchBox';
import authStore from '@/store/auth.store.ts';
interface PatientInfo {
  first_name: string;
  last_name: string;
  patient_id?: string;
  date_of_birth?: string;
  gender?: string;
  dob?: string;
  ur?: string;
}

interface Props {
  extracted: PatientInfo;
  matched: PatientInfo | null;
  measurements: any;
  imageBase64: string;
  selected_file: string;
  onClose: () => void;
  onSuccess: (patientId: string, rft_id: string) => void;
}

interface ResultType {
  error?: string;
  patient_id?: string;
  rft_id?: string;
}

export default function PatientValidationModal({
  extracted,
  matched,
  measurements,
  imageBase64,
  selected_file,
  onClose,
  onSuccess,
}: Props) {
  const patient = extracted;
  const [selectedPatient, setSelectedPatient] = useState<PatientInfo | null>(matched);
  const [createNew, setCreateNew] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [rftExists, setRftExists] = useState(false);
  const siteId = authStore.tokenSiteId;

  const handleConfirm = async (overwriteFlag: boolean = false) => {
    setSubmitting(true);
    setError('');
    setRftExists(false);

    try {
      const formData = new FormData();

      formData.append('file', selected_file); // ✅ must be File, not string
      formData.append('overwrite', String(overwriteFlag));
      formData.append('site_id', String(siteId));
      formData.append('patient_info', JSON.stringify(patient));
      formData.append('rft_measurements', JSON.stringify(measurements));
      formData.append('image_base64', imageBase64);
      formData.append('create_new', JSON.stringify(createNew));
      formData.append('patient_id', selectedPatient?.patient_id || '');

      const response = await fetch('/api/pdf_import/confirm', {method: 'POST',body: formData});

      const result = await response.json();

      if (!response.ok) {
        if (result.error?.includes('RFT session already exists')) {
          setRftExists(true);
          return;
        } else {
          throw new Error(result.error || 'Unknown error');
        }
      }

      if (result.patient_id && result.rft_id) {
        onSuccess(result.patient_id, result.rft_id);
      } else {
        throw new Error('Missing patient_id or rft_id in response');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      {/* RFT Session Already Exists Dialog */}
      {rftExists && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-60">
          <div className="bg-white p-6 rounded shadow-xl w-full max-w-md text-center">
            <h3 className="text-lg font-semibold mb-4 text-red-600">RFT Session Already Exists</h3>
            <p className="mb-6 text-sm text-gray-700">
              A Respiratory Function Test session already exists for this patient on the selected date.
              Do you want to overwrite the existing session?
            </p>
            <div className="flex justify-center gap-4">
              <button
                onClick={() => setRftExists(false)}
                className="px-4 py-2 bg-gray-300 hover:bg-gray-400 rounded"
              >
                Cancel
              </button>
              <button
                onClick={() => handleConfirm(true)}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded"
              >
                Overwrite
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Patient Validation Modal */}
      <div className={`fixed inset-0 bg-black/40 flex justify-center items-center z-50 ${rftExists ? 'pointer-events-none' : ''}`}>
        <div className="bg-white p-6 rounded shadow-xl w-full max-w-2xl">
          <h2 className="text-xl font-semibold mb-6">Validate Patient Info</h2>

          <div className="mb-4">
            <label className="block font-medium mb-1 text-sm text-neutral-700">Search and select a patient (optional):</label>
            <PatientSearchBox
              siteId={String(siteId)}
              onSelect={(p) => {
                setSelectedPatient({
                  ...p,
                  first_name: p.firstname,
                  last_name: p.surname,
                  gender: p.gender_code,
                  date_of_birth: p.dob,
                  patient_id: p.ur,
                });
                setCreateNew(false);
              }}
            />
          </div>

          <div className="grid md:grid-cols-2 gap-4 mb-4">
            {/* Extracted Patient */}
            <div className="bg-gray-50 border p-4 rounded shadow-sm">
              <h3 className="font-semibold text-gray-700 mb-2">📄 Extracted Patient:</h3>
              <p><strong>Name:</strong> {patient.first_name} {patient.last_name}</p>
              <p><strong>UR:</strong> {patient.patient_id || '(none)'}</p>
              <p><strong>DOB:</strong> {patient.date_of_birth || '(none)'}</p>
              <p><strong>Gender:</strong> {patient.gender || '(none)'}</p>
            </div>

            {/* Selected/Matched Patient */}
            {selectedPatient ? (
              <div className="bg-gray-100 border p-4 rounded shadow-sm">
                <h3 className="font-semibold text-gray-700 mb-2">🧩 Selected Patient:</h3>
                <p><strong>Name:</strong> {selectedPatient.first_name} {selectedPatient.last_name}</p>
                <p><strong>UR:</strong> {selectedPatient.ur}</p>
                <p><strong>DOB:</strong> {selectedPatient.dob}</p>
                <p><strong>Gender:</strong> {selectedPatient.gender}</p>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-400 p-4 rounded shadow-sm text-yellow-800">
                <h3 className="font-semibold mb-2">🧩 Selected Patient:</h3>
                <p>No matching patient found or selected.</p>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2 mb-2 text-sm">
            <input
              type="checkbox"
              checked={createNew}
              onChange={(e) => setCreateNew(e.target.checked)}
            />
            Create new patient (override selection)
          </div>

          {error && <p className="text-red-600 text-sm mb-2">{error}</p>}

          <div className="flex justify-end gap-4 mt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-sm rounded"
            >
              Cancel
            </button>
            <button
              onClick={() => handleConfirm()}
              disabled={submitting}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? 'Saving...' : 'Confirm & Save'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
