import { useState, useRef } from 'react';
import PatientValidationModal from './components/PatientValidationModal';
import {useNavigate} from 'react-router';
import { useEffect } from 'react';
import authStore from "@/store/auth.store.ts";

function PdfImport() {
  interface S3FileMetadata {
    template_key: string;
    instruction_key: string;
    filename: string;
    manufacturer: string;
    model: string;
    template: string;
  }


  interface S3PresignedUrlResponse {
    url: string;
  }
  const siteId = authStore.tokenSiteId;
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<any | null>(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const [s3Files, setS3Files] = useState<S3FileMetadata[]>([]);
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile) return;

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('site_id',  String(siteId)); // Replace with actual site ID if needed

    setLoading(true);
    try {
      const response = await fetch('/api/pdf_import', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Upload failed');
      const result = await response.json();
      setParsedData(result);
    } catch (err) {
      console.error(err);
      setSuccessMessage(`❌ Failed to extract patient data.

      📞 Contact Cardiobase Support:
      Phone: +61 3 9439 0221
      Email: <EMAIL>`);
      setTimeout(() => setSuccessMessage(''), 15000);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    const fetchFiles = async () => {
      try {
        const res = await fetch('/api/s3_list_files', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ prefix: 'device-template/' }),
        });

        if (!res.ok) throw new Error('Failed to fetch file list');
        const { files } = await res.json() as { files: S3FileMetadata[] };
        setS3Files(files);
      } catch (err) {
        console.error(err);
      }
    };

    fetchFiles();
  }, []);

  const downloadS3File = async (key: string) => {
    try {
      const response = await fetch('/api/s3_file_url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ key }),
      });

      if (!response.ok) throw new Error('Failed to fetch download URL');

      const { url } = await response.json() as S3PresignedUrlResponse;

      // Extract filename from the key or fallback
      const filename = key.split('/').pop() || 'download.pdf';

      // Create and click a temporary anchor element
      const anchor = document.createElement('a');
      anchor.href = url;
      anchor.download = filename;
      document.body.appendChild(anchor);
      anchor.click();
      document.body.removeChild(anchor);
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download file.');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6 items-center">
      <div
        onClick={() => fileInputRef.current?.click()}
        onDrop={(e) => {
          e.preventDefault();
          setDragActive(false);
          const file = e.dataTransfer.files?.[0];
          if (file?.type === 'application/pdf') setSelectedFile(file);
        }}
        onDragOver={(e) => {
          e.preventDefault();
          setDragActive(true);
        }}
        onDragLeave={(e) => {
          e.preventDefault();
          setDragActive(false);
        }}
        className={`w-full max-w-3xl h-64 border-dashed border-4 rounded-lg flex items-center justify-center cursor-pointer transition-colors text-center px-8 ${
          dragActive ? 'border-blue-500 bg-blue-100' : 'border-gray-400 bg-gray-50'
        }`}
      >
        <p className="text-lg text-gray-600">
          {selectedFile
            ? `📄 Selected: ${selectedFile.name}`
            : '📂 Drag and drop a PDF file here, or click to browse'}
        </p>
        <input
          ref={fileInputRef}
          type="file"
          accept="application/pdf"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) setSelectedFile(file);
          }}
          className="hidden"
        />
      </div>

      {selectedFile && (
        <button
          type="submit"
          disabled={loading}
          className={`px-6 py-3 rounded-md text-base ${
            loading
              ? 'bg-gray-400 cursor-not-allowed text-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {loading ? 'Processing...' : 'Extract PDF'}
        </button>
      )}

      {successMessage && (
        <div className="mt-4 text-center text-green-700 bg-green-100 border border-green-300 px-4 py-2 rounded whitespace-pre-line">
          {successMessage}
        </div>
      )}


      {parsedData && (
        <PatientValidationModal
          extracted={parsedData.patient_info}
          matched={parsedData.matched_patient}
          measurements={parsedData.rft_measurements}
          imageBase64={parsedData.image_base64}
          selected_file={selectedFile}
          onClose={() => setParsedData(null)}
          onSuccess={(patientId: string, rft_id: string) => {
            setSuccessMessage('✅ Patient saved successfully');
            setParsedData(null);
            setSelectedFile(null);
            setTimeout(() => setSuccessMessage(''), 5000);
            navigate(`/patients/${patientId}/rft/${rft_id}?edit=true`);
          }}
        />
      )}
      <div className="w-full max-w-3xl mt-6 p-4 border rounded-md bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-3 text-gray-700">📁 Download Rezibase Magic Import Templates</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {s3Files.map(({ template_key, instruction_key, filename, manufacturer, model, template }) => (
            <div key={template_key} className="border rounded-lg shadow p-4 bg-gray-50">
              <h3 className="text-md font-bold text-gray-800 mb-2">{filename}</h3>
              <p className="text-sm text-gray-600"><strong>Manufacturer:</strong> {manufacturer}</p>
              <p className="text-sm text-gray-600"><strong>Device Model:</strong> {model}</p>
              <p className="text-sm text-gray-600"><strong>Template Type:</strong> {template}</p>
              <div className="flex gap-2 mt-3">
                <button
                  onClick={() => downloadS3File(template_key)}
                  className="px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Template
                </button>
                <button
                  onClick={() => downloadS3File(instruction_key)}
                  className="px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Instruction
                </button>
              </div>
            </div>
          ))}
        </div>


      </div>

    </form>
  );
}

export default PdfImport;
