import {clsx} from 'clsx';
import {useContext} from 'react';
import {
  Button as ButtonBase,
  Collection,
  Dialog,
  DialogTrigger,
  Popover,
  SelectStateContext,
  Tree,
  TreeItem,
  TreeItemContent,
} from 'react-aria-components';
import {useNavigate, useParams} from 'react-router';
import {useAsyncList} from 'react-stately';

import {useLocalStorage} from '@mantine/hooks';
import {ChevronDown, ChevronRight, Computer, Filter, FlaskConical, Star, Weight} from 'lucide-react';

import {Paths} from '@/api-types/routePaths.ts';
import {ControlMethod, Equipment} from '@/api-types/schema.ts';
import {axiosInstance} from '@/axios.ts';
import Skeleton from '@/components/ui/skeleton/skeleton.tsx';
import {apiQueryOptions, useApiQuery} from '@/hooks/use-api-query.ts';
import {queryClient} from '@/query-client.ts';
import {Parameter} from '@/types';
import useControlMethodSelection from '@/views/quality-control/use-control-method-selection';

const skeletonsData = [...Array(3)].map((__n, index) => ({
  id: `loading_${index}`,
  type: 'loading',
}));

export interface LastVisited {
  device?: {id: string};
  parameter?: {id: string};
}

function ControlMethodTreeItem({item, view}: {item: any; view: 'parameter' | 'device'}) {
  const typedItem = item as {
    id: string;
    name: string;
    type: 'lab' | 'device' | 'parameter' | 'ctrlMethod';
    control_methods_equipments_id?: number;
    parent?: {id: string; parent?: {id: string}};
  };
  const [, setLastVisited] = useLocalStorage<LastVisited>({
    key: 'rezibase:last_visited',
    getInitialValueInEffect: false,
  });

  const {controlEquipmentId, controlId, parameterId, deviceId, labId} = useParams();
  const selectedItemKey = [view, labId, deviceId ?? parameterId, controlId ?? controlEquipmentId]
    .filter(Boolean)
    .join('/');

  const {data: fav} = useApiQuery(Paths.GET_FAVOURITE);

  const selectState = useContext(SelectStateContext);
  const navigate = useNavigate();

  const itemsList = useAsyncList<Equipment | Parameter | ControlMethod>({
    load: async () => {
      if (typedItem.type === 'lab') {
        const items = await queryClient.ensureQueryData(
          apiQueryOptions(
            view === 'device' ? Paths.QC_EQUIPMENT_LIST : Paths.QC_PARAMETERS_FILTER,
            {},
            {lab_id: typedItem.id}
          )
        );
        return {items};
      }

      if (typedItem.type === 'device') {
        const ctrlMethods = await queryClient.ensureQueryData(
          apiQueryOptions(Paths.QC_CONTROL_METHODS_LIST, {}, {equipment_id: typedItem.id})
        );
        return {items: ctrlMethods};
      }

      if (typedItem.type === 'parameter') {
        const parameters = await queryClient.ensureQueryData(
          apiQueryOptions(Paths.QC_CONTROL_METHOD_FILTER, {}, {parameter_id: typedItem.id})
        );
        return {items: parameters};
      }

      return {items: []};
    },
  });

  const itemId = [
    view,
    typedItem.parent?.parent?.id,
    typedItem.parent?.id,
    typedItem.control_methods_equipments_id ?? typedItem.id,
  ]
    .filter(Boolean)
    .join('/');

  return (
    <TreeItem
      className={clsx(
        'react-aria-TreeItem data-focus-visible:ring-brand-500/40 relative flex h-8 cursor-pointer items-center gap-2 rounded-md p-2 pl-[calc(var(--tree-item-level)*18px)] text-sm outline-hidden',
        'data-focus-visible:ring-3 data-focused:bg-gray-100 data-hovered:bg-gray-100 [&:hover:not(:has([data-star]:where(:hover,:active,:focus)))]:bg-gray-100 [&[data-expanded=true]>div>button>svg:first-child]:rotate-90',
        selectedItemKey === itemId && 'bg-brand-400! text-white',
        itemsList.items.length === 0 &&
          item.type !== 'ctrlMethod' &&
          !itemsList.isLoading &&
          'pointer-events-none text-neutral-600'
      )}
      data-id={itemId}
      textValue={item.model ?? item.abbreviation ?? item.name}
      id={itemId}
    >
      <TreeItemContent>
        {typedItem.type === 'ctrlMethod' ? (
          <>
            <ButtonBase
              onPress={() => {
                selectState?.close();
                navigate(
                  `/quality-control/${itemId.replace(/\b(device|parameter)\b/, (match: string) => `${match}s`)}`
                );
                setLastVisited((prev) => ({
                  ...prev,
                  ...(view === 'device' && {
                    device: {...prev?.device, id: itemId.replace('device', 'devices')},
                  }),
                  ...(view === 'parameter' && {
                    parameter: {...prev?.parameter, id: itemId.replace('parameter', 'parameters')},
                  }),
                }));
              }}
              className="absolute inset-0 z-1 focus:outline-hidden"
            />

            <div className="block w-2.5" />
            <div className="flex items-center gap-x-2">
              <div className="bg-brand-400 w-1 rounded p-1" />
              <div>{item.name}</div>
            </div>
          </>
        ) : (
          <>
            <ButtonBase
              isDisabled={itemsList.items.length === 0}
              slot="chevron"
            >
              <ChevronRight className="size-4 transition-transform" />
            </ButtonBase>
            <div className="flex items-center gap-x-2">
              {item.type === 'lab' && <FlaskConical className="w-4" />}
              {item.type === 'device' && <Computer className="w-4" />}
              {item.type === 'parameter' && <Weight className="w-4" />}
              <div>
                {item.type === 'lab' && item.name}
                {item.type === 'device' && item.name}
                {item.type === 'parameter' && <>{item.abbreviation}</>}
              </div>
            </div>
          </>
        )}

        {typedItem.type === 'ctrlMethod' && (
          <button
            data-star=""
            className={clsx(
              'absolute top-0 right-0 z-10 rounded-md p-2 hover:bg-gray-100',
              selectedItemKey === itemId && 'hover:bg-transparent'
            )}
            onClick={() => {
              axiosInstance
                .post(Paths.FAVOURITE, {
                  type: view,
                  item_id: itemId,
                })
                .then(() => queryClient.invalidateQueries(apiQueryOptions(Paths.GET_FAVOURITE)));
            }}
          >
            <Star
              className={clsx(
                'h-4 w-4',
                fav?.[view]?.item_id === itemId ? 'text-yellow-500' : 'text-neutral-500',
                selectedItemKey === itemId && 'text-white/80'
              )}
              fill={fav?.[view]?.item_id === itemId ? 'currentColor' : 'none'}
            />
          </button>
        )}
      </TreeItemContent>

      <Collection
        items={
          (itemsList.isLoading ? skeletonsData : (itemsList.items ?? [])) as (
            | Equipment
            | ControlMethod
            | Parameter
            | {
                type: 'loading';
                id: string;
              }
          )[]
        }
      >
        {(element) =>
          itemsList.isLoading ? (
            <TreeItem
              className="pl-[calc(var(--tree-item-level)*18px)]"
              id={(element as any).type + element.id + typedItem.id + '-' + (typedItem.parent?.id ?? '')}
              textValue={element.id.toString()}
            >
              <TreeItemContent>
                <Skeleton
                  height={10}
                  width={100}
                />
              </TreeItemContent>
            </TreeItem>
          ) : itemsList.error ? (
            <TreeItem
              className="pl-[calc(var(--tree-item-level)*18px)]"
              id={(element as any).type + element.id + typedItem.id + '-' + (typedItem.parent?.id ?? '')}
              textValue={element.id.toString()}
            >
              <TreeItemContent>
                <p className="px-2 text-sm text-red-400">{`Unable to fetch "${typedItem.type === 'lab' ? 'devices' : 'parameters'}"`}</p>
              </TreeItemContent>
            </TreeItem>
          ) : (
            <ControlMethodTreeItem
              view={view}
              item={
                typedItem.type === 'lab'
                  ? {
                      ...element,
                      name: (element as Equipment).model,
                      type: view,
                      parent: typedItem,
                    }
                  : ['device', 'parameter'].includes(typedItem.type)
                    ? {
                        ...element,
                        type: 'ctrlMethod',
                        parent: typedItem,
                      }
                    : element
              }
            />
          )
        }
      </Collection>
    </TreeItem>
  );
}

function SelectedValue({view}: {view: 'parameter' | 'device'}) {
  const {selectedControlMethod, selectedParameter, selectedDevice, selectedLab} = useControlMethodSelection();

  if (!selectedControlMethod) {
    return (
      <div className="flex-1 truncate text-sm font-normal text-gray-500">
        {view === 'device' ? 'Select a Control Method' : 'Select Parameter and Control Method'}
      </div>
    );
  }

  return (
    <div className="flex-1 truncate font-normal">
      <div className="flex items-center gap-1 text-sm text-gray-600">
        {selectedLab?.name}
        <ChevronRight
          strokeWidth={2}
          className="size-3 shrink-0 text-gray-400"
        />
        {selectedDevice?.model ?? selectedParameter?.abbreviation}
        <ChevronRight
          strokeWidth={2}
          className="size-3 shrink-0 text-gray-400"
        />
        {selectedControlMethod.name}
      </div>
    </div>
  );
}

export default function ControlMethodSelector({view = 'device'}: {view?: 'parameter' | 'device'}) {
  const {data: labsData} = useApiQuery(Paths.QC_LABS_LIST);
  const {controlEquipmentId, controlId, parameterId, deviceId, labId} = useParams();

  const selectedKeys = [
    [view, labId].filter(Boolean).join('/'),
    [view, labId, deviceId ?? parameterId].filter(Boolean).join('/'),
    [view, labId, deviceId ?? parameterId, controlId ?? controlEquipmentId].filter(Boolean).join('/'),
  ];

  if (Array.isArray(labsData)) {
    labsData.map((el) =>
      queryClient.fetchQuery(
        apiQueryOptions(
          view === 'device' ? Paths.QC_EQUIPMENT_LIST : Paths.QC_PARAMETERS_FILTER,
          {},
          {lab_id: el.id}
        )
      )
    );
  }

  return (
    <DialogTrigger>
      <ButtonBase className="focus-visible:border-brand2-500 focus-visible:ring-brand2-400/30 flex h-8 min-w-[380px] cursor-pointer items-center gap-x-2 rounded-md border border-gray-300 bg-white py-2 pr-2 pl-4 text-left text-gray-700 transition hover:border-gray-400 focus:outline-hidden focus-visible:ring-2 disabled:bg-gray-50 disabled:text-gray-400 disabled:opacity-100! disabled:ring-gray-200">
        <Filter className="size-4 text-gray-500" />
        <SelectedValue view={view} />
        <ChevronDown
          className="h-5 w-5 text-gray-400"
          aria-hidden="true"
        />
      </ButtonBase>
      <Popover className="react-aria-Popover min-w-[380px]">
        <Dialog>
          <Tree
            className="react-aria-Tree flex flex-col gap-1 text-gray-800 outline-hidden focus:bg-transparent"
            aria-label="Tree"
            items={labsData ?? []}
            defaultExpandedKeys={
              new Set([
                ...selectedKeys,
                ...((labsData ?? []).length === 1 ? [`${view}/${labsData[0].id}`] : []),
              ])
            }
          >
            {(item) => (
              <ControlMethodTreeItem
                view={view}
                item={{...item, type: 'lab'}}
              />
            )}
          </Tree>
        </Dialog>
      </Popover>
    </DialogTrigger>
  );
}
