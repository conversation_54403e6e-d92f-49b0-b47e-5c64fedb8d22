import clsx from 'clsx';
import {useMemo} from 'react';
import {useNavigate, useParams} from 'react-router';

import {useMutation} from '@tanstack/react-query';
import {Cell, type CellContext, createColumnHelper} from '@tanstack/react-table';
import {intlFormat} from 'date-fns';
import {atom, useSetAtom} from 'jotai';
import {MessageSquareDot} from 'lucide-react';

import {Paths} from '@/api-types/routePaths.ts';
import {Parameter, Session, SessionData} from '@/api-types/schema.ts';
import {urlWithArgs} from '@/api-types/url.ts';
import {axiosInstance} from '@/axios.ts';
import DataTable from '@/components/datatable/DataTable.tsx';
import {loadingColumns} from '@/components/datatable/TableSkeleton.tsx';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/tooltip.tsx';
import {apiQueryOptions, useApiQuery} from '@/hooks/use-api-query.ts';
import {queryClient} from '@/query-client.ts';
import ExpandedRow from '@/views/quality-control/components/device-summary/ExpandedRow.tsx';
import useControlMethodSelection from '@/views/quality-control/use-control-method-selection';

import ReviewOrDeleteSessionMenu from '../review-or-delete-session-menu';

const columnHelper = createColumnHelper<Session>();
export const tableSelection$ = atom<Record<
  number,
  {sessionId: number; parameterId: number; parameterValue: number; rowIdx: number}[]
> | null>(null);

function ParameterTableHeader({parameter}: {parameter: Parameter}) {
  const navigate = useNavigate();
  const {labId} = useParams();
  const {selectedControlMethod} = useControlMethodSelection();

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <div className="flex h-full w-full flex-col items-end justify-center">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="cursor-help underline decoration-dotted">
                {parameter.abbreviation ?? parameter.long_name}
              </div>
            </TooltipTrigger>
            <TooltipContent className="bg-black/60">
              {parameter.long_name}
              <br />[{parameter.unit}]
            </TooltipContent>
          </Tooltip>
          <div className="w-20 truncate font-semibold text-neutral-600 normal-case">[{parameter.unit}]</div>
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem
          onClick={() => {
            navigate(`/quality-control/parameters/${labId}/${parameter.id}/${selectedControlMethod?.id}/`);
          }}
        >
          Goto Parameters View
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}

export function SessionsTable() {
  const {controlEquipmentId} = useParams();
  const setTableSelection = useSetAtom(tableSelection$);

  const {data: parameters, isLoading: isParamsLoading} = useApiQuery(
    Paths.QC_PARAMETERS,
    {},
    {control_methods_equipments_id: controlEquipmentId},
    {
      enabled: !!controlEquipmentId,
    }
  );

  const {data: sessions, isLoading: isSessionsLoading} = useApiQuery(
    Paths.QC_SESSIONS,
    {},
    {control_method_equipment_id: controlEquipmentId},
    {
      enabled: !!controlEquipmentId,
    }
  );

  const {data: alarms, isLoading: isAlarmsLoading} = useApiQuery(
    Paths.QC_ALARMS,
    {},
    {
      control_method_equipment_id: controlEquipmentId,
    }
  );

  const sessionsApiQueryOptions = apiQueryOptions(
    Paths.QC_SESSIONS,
    {},
    {control_method_equipment_id: controlEquipmentId},
    {
      enabled: !!controlEquipmentId,
    }
  );

  const {data: users} = useApiQuery(Paths.QC_USERS);

  const {mutateAsync} = useMutation<
    unknown,
    unknown,
    [Cell<Session, SessionData>, string],
    {
      previousSessions: Session[] | undefined;
    }
  >({
    mutationKey: [...sessionsApiQueryOptions.queryKey, 'mutate'],
    mutationFn: ([cell, value]) => {
      const rowValue = cell.row.original;
      const parameterId = (cell.column.columnDef.meta as any)?.parameterId;

      return axiosInstance.patch(urlWithArgs(Paths.QC_SESSIONS_DETAIL, {sessionId: rowValue.id}), {
        session_id: rowValue.id,
        control_methods_equipments_parameter_id: parameterId,
        value,
      });
    },
    onMutate: async ([cell, val]) => {
      const cellValue = cell.getValue();
      const rowValue = cell.row.original;
      const parameterId = (cell.column.columnDef.meta as any)?.parameterId;

      await queryClient.cancelQueries(sessionsApiQueryOptions);
      const previousSessions = queryClient.getQueryData(sessionsApiQueryOptions.queryKey);

      queryClient.setQueryData(sessionsApiQueryOptions.queryKey, (oldSessions) => {
        return oldSessions?.map((session) => {
          if (session.id !== rowValue.id) {
            return session;
          }

          const sessionDatas = session.session_datas.filter(
            (sd) => sd.control_method_equipment_parameter_id !== parameterId
          );

          if (cellValue) {
            cellValue.parameter_value = parseFloat(val);
          }

          return {
            ...session,
            session_datas: [
              ...sessionDatas,
              cellValue ?? {
                control_method_equipment_parameter_id: parameterId,
                parameter_value: parseFloat(val),
                session_id: rowValue.id,
              },
            ],
          };
        });
      });

      return {previousSessions};
    },
    onError: (_error, _newData, context) => {
      queryClient.setQueryData(sessionsApiQueryOptions.queryKey, context?.previousSessions);
    },
    onSettled: () => {
      queryClient.invalidateQueries(sessionsApiQueryOptions);
    },
  });

  const columns = useMemo(
    () => [
      columnHelper.display({
        id: 'index',
        header: '',
        meta: {
          tdProps: {
            className: 'text-gray-400 tabular-nums text-left text-xs w-16',
          },
        },
        cell: ({row}) => row.index + 1,
      }),
      columnHelper.accessor((row) => new Date(row.session_time), {
        id: 'session_time',
        header: 'Session Time',
        sortingFn: 'datetime',
        enableSorting: true,
        meta: {
          type: 'date',
        },
        cell: ({row}) => {
          const notes = row?.original?.session_datas?.map((sessionData) => sessionData?.notes)?.[0];
          return (
            <div className="flex max-w-44 items-center justify-between gap-x-4">
              <span className="text-neutral-900">
                {intlFormat(
                  new Date(row.original.session_time),
                  {
                    dateStyle: 'medium',
                    timeStyle: 'short',
                    hour12: false,
                  },
                  {locale: 'en-GB'}
                )}
              </span>
              <div className="">
                {!!notes && (
                  <Tooltip>
                    <TooltipTrigger>
                      <MessageSquareDot className="text-brand2-600 h-4 w-4 text-sm" />
                    </TooltipTrigger>
                    <TooltipContent className={!notes ? 'hidden' : 'max-w-md whitespace-pre-wrap'}>
                      {notes}
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
            </div>
          );
        },
      }),
      ...(isParamsLoading
        ? loadingColumns(3, {
            thProps: {
              className: 'w-24 text-right',
            },
            tdProps: {
              className: 'w-24 text-right',
            },
          })
        : (parameters ?? []).map((p) =>
            columnHelper.accessor(
              (row) => {
                return (row as Session)?.session_datas?.find(
                  (v) => v.control_method_equipment_parameter_id === p.control_methods_equipments_parameter_id
                );
              },
              {
                header: () => <ParameterTableHeader parameter={p} />,
                cell: (cell) => {
                  const sessionData = cell.getValue();

                  if (typeof sessionData?.parameter_value === 'number' && p.decimal_places) {
                    const nf = new Intl.NumberFormat('en', {
                      minimumFractionDigits: p.decimal_places,
                    });
                    const cellData = cell.getValue();
                    const alarm = cellData?.alarms?.[0];

                    return (
                      <>
                        {cellData?.is_in_control ? (
                          <div
                            className=""
                            data-session-id={sessionData.session_id}
                            data-session-id2={cell.row.original.id}
                          >
                            {nf.format(sessionData?.parameter_value)}
                          </div>
                        ) : (
                          <Tooltip>
                            <TooltipTrigger
                              className={alarm?.open ? 'cursor-help underline decoration-dotted' : ''}
                            >
                              {nf.format(sessionData?.parameter_value)}
                            </TooltipTrigger>
                            <TooltipContent
                              className={clsx(
                                'mb-4 bg-red-50 text-red-700 shadow-lg',
                                !alarm?.open && 'hidden'
                              )}
                            >
                              {alarm?.relevant_control_rules?.map((error) => (
                                <div key={error?.name}>
                                  <p className="font-semibold">Error: {error?.name}</p>
                                  <p className="mb-1.5">{error?.description}</p>
                                </div>
                              ))}
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </>
                    );
                  }

                  return cell.getValue()?.parameter_value;
                },
                // cell: EditableElement as any,
                id: p.id.toString(),
                enableSorting: false,
                meta: {
                  type: 'number',
                  isEditable: true,
                  isSelectable: true,
                  parameterId: p.control_methods_equipments_parameter_id,
                  decimalPlaces: p.decimal_places,
                  editableValue: (v: SessionData) => v?.parameter_value,
                  thProps: {
                    className: 'w-24 text-right',
                  },
                  tdProps: (cell: CellContext<Session, SessionData>) => {
                    const cellData = cell.getValue();
                    const alarm = cellData?.alarms?.[0];

                    return {
                      className: clsx(
                        'w-24 text-right',
                        cellData?.is_in_control && 'bg-amber-50 text-amber-700',
                        alarm?.open && !cellData?.is_in_control && 'bg-red-50 text-red-700',
                        alarm && !alarm?.open && 'bg-blue-100 text-blue-800'
                      ),
                    };
                  },
                },
              }
            )
          )),
      columnHelper.display({
        id: 'actions',
        header: '',
        cell: (cellContext) => {
          const cellData = cellContext.row?.original;
          const alarm = cellData?.session_datas?.filter(
            (sessionData) => sessionData?.alarms?.length > 0
          )?.[0];
          return (
            <ReviewOrDeleteSessionMenu
              expandRowToggleable={() => {
                cellContext.table.resetExpanded();
                cellContext.row.toggleExpanded();
              }}
              sessionId={cellContext?.row?.original?.id!}
              controlMethodEquipmentId={cellContext?.row?.original?.control_method_equipment_id!}
              hasViolations={!!alarm}
            />
          );
        },
        meta: {
          cellLoading: () => null,
          thProps: {
            className: 'max-w-12',
          },
          tdProps: {
            className: 'text-right',
          },
        },
      }),
    ],
    [parameters, isParamsLoading, alarms, sessions]
  );

  return (
    <DataTable
      data={sessions ?? []}
      enableSortingRemoval={false}
      expandedRow={(row) => (
        <ExpandedRow
          users={users}
          row={row}
        />
      )}
      columns={columns as any}
      isLoading={isParamsLoading || isSessionsLoading || isAlarmsLoading}
      onValueEdit={(cell, value) => mutateAsync([cell as Cell<Session, SessionData>, value])}
      onSelectionChange={(selection, table) => {
        const selectedCells = Array.from(selection)
          .map((e) => (e as string).split('_'))
          .filter((e) => e.length === 2)
          .map(([rowIdx, colIdx]) => [+rowIdx, +colIdx] as const)
          .map(([rowIdx, colIdx]) => {
            const row = table.getRowModel().rows[rowIdx];
            const cell = row.getVisibleCells()[colIdx];
            const value = cell?.getValue() as SessionData | undefined;

            return {
              sessionId: value?.session_id!,
              parameterId: value?.control_method_equipment_parameter_id!,
              parameterValue: value?.parameter_value,
              rowIdx: cell.row.index,
            } as const;
          })
          .filter(({parameterId}) => !!parameterId);

        if (selectedCells.length > 1) {
          const parameterSelection = Object.groupBy(selectedCells, (cell) => cell.parameterId);
          setTableSelection(parameterSelection as any);
        } else {
          setTableSelection(null);
        }
      }}
      initialState={{
        sorting: [
          {
            id: 'session_time',
            desc: true,
          },
        ],
      }}
    />
  );
}
