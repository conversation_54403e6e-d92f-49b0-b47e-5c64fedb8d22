import {ComponentProps} from 'react';
import {
  Dialog,
  Heading,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  SelectValue,
} from 'react-aria-components';
import {useLocation, useNavigate, useParams} from 'react-router';

import {useMutation, useQuery} from '@apollo/client';
import {ChevronDown, X} from 'lucide-react';

import {Input, Label, TextArea} from '@/components/ui/Field';
import {But<PERSON>} from '@/components/ui/button';
import {Form, FormRootErrors, NumberFormField, SelectFormField, TextFormField} from '@/components/ui/form';
import {WatchState} from '@/components/ui/form/WatchState';
import {
  createEquation,
  getAgeGroups,
  getPredGenders,
  getStatTypes,
  updateEquation,
} from '@/graphql/equations';
import {getEquation, getTestDetail, getTestEquations} from '@/graphql/normal-values.ts';
import {getEthnicities} from '@/graphql/patients';
import {AllParameters} from "@/graphql/common.ts";

export default function EquationAddEditDrawer(props: ComponentProps<typeof Modal>) {
  const {equationId, testId} = useParams<{equationId: string; testId: string}>();
  const location = useLocation();
  const navigate = useNavigate();

  // Parse query parameters
  const queryParams = new URLSearchParams(location.search);
  const sourceId = queryParams.get('sourceId');
  const sourceName = queryParams.get('sourceName');
  const parameterId = queryParams.get('parameterId');
  const parameterName = queryParams.get('parameterName');

  const isAddMode = equationId === 'add';

  // Get test details if testId is provided
  const {data: testData} = useQuery(getTestDetail, {
    variables: {testid: testId ? +testId : 0},
    skip: !testId,
  });

  const {data: equationData} = useQuery(getEquation, {
    variables: {equationId: isAddMode ? 0 : +equationId!},
    skip: isAddMode || !equationId,
  });

  const {data: statTypesData} = useQuery(getStatTypes);
  const {data: gendersData} = useQuery(getPredGenders);
  const {data: ageGroupsData} = useQuery(getAgeGroups);
  const {data: ethnicitiesData} = useQuery(getEthnicities);
  const {data: parametersData} = useQuery(AllParameters);

  const [updateEquationMutation, {loading: updateLoading}] = useMutation(updateEquation, {
    refetchQueries: [getTestEquations],
  });

  const [createEquationMutation, {loading: createLoading}] = useMutation(createEquation, {
    refetchQueries: [getTestEquations],
  });

  const loading = updateLoading || createLoading;

  const handleFormSubmit = async (formData: any) => {
    // Map the required IDs based on selected values
    const selectedAgeGroup = ageGroupsData?.pred_ref_agegroups.find(
      (item) => item.agegroup === formData.agegroup
    );

    const selectedStatType = statTypesData?.pred_ref_stattypes.find(
      (item) => item.stattype === formData.stattype_range
    );

    const selectedEthnicity = ethnicitiesData?.pred_ref_ethnicities.find(
      (item) => item.description === formData.ethnicity
    );

    const selectedGender = gendersData?.pred_ref_genders.find((g) => g.gender === formData.gender);

    delete formData.id;
    delete formData.__typename;


    if (!formData.equation_range && formData.stattype_range !== 'range' && formData.equation_mpv) {
      formData.equation_range = formData.equation_mpv;
    }

    // Prepare the equation data with all required IDs
    const equationPayload = {
      ...formData,
      // Convert numeric strings to strings for consistency
      age_lower: formData.age_lower.toString(),
      age_upper: formData.age_upper.toString(),
      ht_lower: formData.ht_lower.toString(),
      ht_upper: formData.ht_upper.toString(),
      wt_lower: formData.wt_lower.toString(),
      wt_upper: formData.wt_upper.toString(),

      // Set the selected IDs from reference data
      agegroupid: selectedAgeGroup?.agegroupid ?? null,
      stattype_rangeid: selectedStatType?.stattypeid ?? null,
      ethnicityid: selectedEthnicity?.ethnicityid ?? null,
      genderid: selectedGender?.id ?? null,

      // Set test information
      testid: testId ? +testId : null,
      test: testData?.pred_ref_tests[0]?.test ?? null,

      // Set source and parameter information if provided
      sourceid: sourceId ? +sourceId : null,
      parameterid: parameterId ? +parameterId : null,
    };

    try {
      if (isAddMode) {
        // Create new equation
        await createEquationMutation({
          variables: {
            equation: equationPayload,
          },
        });
      } else {
        // Update existing equation
        await updateEquationMutation({
          variables: {
            equationId: +equationId!,
            equation: equationPayload,
          },
        });
      }

      // Navigate back to the previous page on success
      navigate(window.location.pathname.split('/').slice(0, -1).join('/'), {
        replace: true,
      });
    } catch (error) {
      console.error('Error saving equation:', error);
    }
  };

  // Define initial values for the form
  const initialValues = isAddMode
    ? {
        test: testData?.pred_ref_tests[0]?.test || '',
        source: sourceName || '',
        parameter: parameterName || '',
        equation_mpv: '',
        equation_range: '',
        equation_zscore: '',
        gender: '',
        agegroup: '',
        ethnicity: '',
        stattype_range: '',
      }
    : equationData?.pred_equations[0];

  return (
    <Modal
      isDismissable
      className="react-aria-Drawer"
      isOpen={!!equationId}
      onOpenChange={() => {
        navigate(window.location.pathname.split('/').slice(0, -1).join('/'), {
          replace: true,
        });
      }}
      {...props}
    >
      <Dialog>
        <Heading slot="title">{isAddMode ? 'Add Equation' : 'Edit Equation'}</Heading>
        <RACButton slot="close">
          <X />
        </RACButton>

        <Form
          key={isAddMode ? 'add-equation' : equationData?.pred_equations[0]?.equationid}
          values={initialValues}
          className="mt-6 flex-grow"
          onSubmit={handleFormSubmit}
          id="equation-form"
        >
          <div className="space-y-3 pb-6">
            <FormRootErrors />

            <div className="grid grid-cols-2 items-center gap-x-3">
              <TextFormField
                required
                name="test"
              >
                <Label>Test</Label>
                <Input size="md" />
              </TextFormField>

              <TextFormField
                required={!!sourceName}
                name="source"
              >
                <Label>Source</Label>
                <Input size="md" />
              </TextFormField>
            </div>

            {(!!parameterName || !isAddMode) ? (
              <TextFormField
                required
                name="parameter"
              >
                <Label>Parameter</Label>
                <Input size="md" />
              </TextFormField>
            ) : (
              <SelectFormField required name="parameter">
                <Label>Parameter</Label>
                <RACButton className="react-aria-Button w-full">
                  <SelectValue className="react-aria-SelectValue text-sm/[1.3]" />
                  <ChevronDown
                    className="size-4 text-gray-400"
                    aria-hidden="true"
                  />
                </RACButton>
                <Popover>
                  <ListBox items={parametersData?.pred_ref_parameters ?? []}>
                    {(item) => <ListBoxItem id={item.description?.toString()}>{item.description}</ListBoxItem>}
                  </ListBox>
                </Popover>
              </SelectFormField>
            )}

            <div className="grid grid-cols-2 items-center gap-x-3">
              <SelectFormField name="agegroup">
                <Label>Age Group</Label>
                <RACButton className="react-aria-Button w-full">
                  <SelectValue className="react-aria-SelectValue text-sm/[1.3]" />
                  <ChevronDown
                    className="size-4 text-gray-400"
                    aria-hidden="true"
                  />
                </RACButton>
                <Popover>
                  <ListBox items={ageGroupsData?.pred_ref_agegroups ?? []}>
                    {(item) => <ListBoxItem id={item.agegroup?.toString()}>{item.agegroup}</ListBoxItem>}
                  </ListBox>
                </Popover>
              </SelectFormField>

              <SelectFormField name="gender">
                <Label>Gender</Label>
                <RACButton className="react-aria-Button w-full">
                  <SelectValue className="react-aria-SelectValue text-sm/[1.3]" />
                  <ChevronDown
                    className="size-4 text-gray-400"
                    aria-hidden="true"
                  />
                </RACButton>
                <Popover>
                  <ListBox items={gendersData?.pred_ref_genders ?? []}>
                    {(item) => <ListBoxItem id={item.gender?.toString()}>{item.gender}</ListBoxItem>}
                  </ListBox>
                </Popover>
              </SelectFormField>
            </div>

            <div>
              <Label>Age</Label>
              <div className="isolate flex items-center -space-x-px">
                <NumberFormField
                  name="age_lower"
                  className="relative flex-1"
                  aria-label="Age min"
                >
                  <div className="pointer-events-none absolute inset-y-0 left-3 z-30 flex items-center text-xs font-medium text-neutral-700">
                    Lower:
                  </div>
                  <Input
                    className="rounded-r-none pl-13"
                    size="md"
                  />
                </NumberFormField>
                <NumberFormField
                  name="age_upper"
                  className="relative flex-1"
                  aria-label="Age max"
                >
                  <div className="pointer-events-none absolute inset-y-0 left-3 z-30 flex items-center text-xs font-medium text-neutral-700">
                    Upper:
                  </div>
                  <Input
                    className="rounded-l-none pl-13"
                    size="md"
                  />
                </NumberFormField>
              </div>
            </div>

            <div>
              <Label>Height (cm)</Label>
              <div className="isolate flex items-center -space-x-px">
                <NumberFormField
                  name="ht_lower"
                  className="relative flex-1"
                  formatOptions={{
                    style: 'unit',
                    unit: 'centimeter',
                    unitDisplay: 'short',
                  }}
                  aria-label="Age min"
                >
                  <div className="pointer-events-none absolute inset-y-0 left-3 z-30 flex items-center text-xs font-medium text-neutral-700">
                    Lower:
                  </div>
                  <Input
                    className="rounded-r-none pl-13"
                    size="md"
                  />
                </NumberFormField>
                <NumberFormField
                  name="ht_upper"
                  className="relative flex-1"
                  formatOptions={{
                    style: 'unit',
                    unit: 'centimeter',
                    unitDisplay: 'short',
                  }}
                  aria-label="Age max"
                >
                  <div className="pointer-events-none absolute inset-y-0 left-3 z-30 flex items-center text-xs font-medium text-neutral-700">
                    Upper:
                  </div>
                  <Input
                    className="rounded-l-none pl-13"
                    size="md"
                  />
                </NumberFormField>
              </div>
            </div>

            <div>
              <Label>Weight (kg)</Label>
              <div className="isolate flex items-center -space-x-px">
                <NumberFormField
                  name="wt_lower"
                  className="relative flex-1"
                  formatOptions={{
                    style: 'unit',
                    unit: 'kilogram',
                    unitDisplay: 'short',
                  }}
                  aria-label="Age min"
                >
                  <div className="pointer-events-none absolute inset-y-0 left-3 z-30 flex items-center text-xs font-medium text-neutral-700">
                    Lower:
                  </div>
                  <Input
                    className="rounded-r-none pl-13"
                    size="md"
                  />
                </NumberFormField>
                <NumberFormField
                  name="wt_upper"
                  className="relative flex-1"
                  formatOptions={{
                    style: 'unit',
                    unit: 'kilogram',
                    unitDisplay: 'short',
                  }}
                  aria-label="Age max"
                >
                  <div className="pointer-events-none absolute inset-y-0 left-3 z-30 flex items-center text-xs font-medium text-neutral-700">
                    Upper:
                  </div>
                  <Input
                    className="rounded-l-none pl-13"
                    size="md"
                  />
                </NumberFormField>
              </div>
            </div>

            <SelectFormField name="ethnicity">
              <Label>Ethnicity</Label>
              <RACButton className="react-aria-Button w-full">
                <SelectValue className="react-aria-SelectValue text-sm/[1.3]" />
                <ChevronDown
                  className="size-4 text-gray-400"
                  aria-hidden="true"
                />
              </RACButton>
              <Popover>
                <ListBox items={ethnicitiesData?.pred_ref_ethnicities ?? []}>
                  {(item) => <ListBoxItem id={item.description?.toString()}>{item.description}</ListBoxItem>}
                </ListBox>
              </Popover>
            </SelectFormField>

            <SelectFormField name="stattype_range">
              <Label>Range Type</Label>
              <RACButton className="react-aria-Button w-full">
                <SelectValue className="react-aria-SelectValue text-sm/[1.3]" />
                <ChevronDown
                  className="size-4 text-gray-400"
                  aria-hidden="true"
                />
              </RACButton>
              <Popover>
                <ListBox items={statTypesData?.pred_ref_stattypes ?? []}>
                  {(item) => <ListBoxItem id={item.stattype?.toString()}>{item.stattype}</ListBoxItem>}
                </ListBox>
              </Popover>
            </SelectFormField>

            <WatchState name="stattype_range">
              {(value) =>
                typeof value === 'string'? (
                  <TextFormField name="equation_range">
                    <Label>Equation Range</Label>
                    <TextArea className="font-mono" size="md" />
                  </TextFormField>
                ) : null
              }
            </WatchState>

            <TextFormField name="equation_mpv">
              <Label>Equation MPV</Label>
              <TextArea
                className="font-mono"
                size="md"
              />
            </TextFormField>

            <TextFormField name="equation_zscore">
              <Label>Equation Z-Score</Label>
              <TextArea
                className="font-mono"
                size="md"
              />
            </TextFormField>
          </div>

          <div className="fixed inset-x-0 bottom-0 flex justify-end space-x-3 border-t border-gray-200 bg-white p-4 sm:px-6">
            <Button
              variant="outlined"
              onPress={() => {
                navigate(window.location.pathname.split('/').slice(0, -1).join('/'), {
                  replace: true,
                });
              }}
            >
              Cancel
            </Button>
            <Button type="submit">
              {loading ? 'Saving...' : isAddMode ? 'Add Equation' : 'Save Changes'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}
