import {ReactNode, useState} from 'react';
import {Dialog, Modal, Switch} from 'react-aria-components';

import {ChevronDown, X as XIcon} from 'lucide-react';
import {useQueryState} from 'nuqs';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import CalendarIcon from '@/assets/iconly/Calendar.svg?react';
import ManWomanIcon from '@/assets/iconly/ManWoman.svg?react';
import WorldLocationIcon from '@/assets/iconly/WorldLocation.svg?react';
import AvatarIcon from '@/components/icons/AvatarIcon.tsx';
import {TranslatelanguageIconly} from '@/components/icons/TranslateLanguageIconly';
import {useDialogState} from '@/components/modal-state-provider';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from '@/components/ui/collapsible';
import {getDoctorsData, mutateDoctorData} from '@/graphql/contacts';
import EditableItem from '@/views/patients/components/EditableItem';

export default function EditContact({
  doctor,
}: {
  doctor: QueryResultType<typeof getDoctorsData>['doctors'][0] | undefined;
}) {
  const [isReferring, setIsReferring] = useState(doctor?.is_referring ?? false);
  const [isOpen, setIsOpen] = useDialogState('edit-contact');
  const [, setSelectedDoctorId] = useQueryState('doctorId');

  if (!doctor) {
    return null;
  }

  const identification = [
    {
      id: doctor?.provider_number?.toString(),
      defaultValue: doctor?.provider_number,
      label: 'Provider Number',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'provider_number',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.gp_code?.toString(),
      defaultValue: doctor?.gp_code?.toString(),
      label: 'GP Code',
      type: 'string',
      StartAdornmentIcon: <ManWomanIcon className="size-5 text-neutral-500" />,
      mutateFn: mutateDoctorData,
      fieldName: 'gp_code',
    },
    {
      id: doctor?.practice_code?.toString(),
      defaultValue: doctor?.practice_code?.toString(),
      label: 'Practice Code',
      type: 'string',
      StartAdornmentIcon: <WorldLocationIcon className="size-5 text-neutral-500" />,
      fieldName: 'practice_code',
      mutateFn: mutateDoctorData,
    },

    {
      id: doctor?.practice_number?.toString(),
      defaultValue: doctor?.practice_number?.toString(),
      label: 'Practice #',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'practice_number',
      mutateFn: mutateDoctorData,
    },
  ];

  const practiceAddress = [
    {
      id: doctor?.clinic_name?.toString(),
      defaultValue: doctor?.clinic_name,
      label: 'Clinic Name',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'clinic_name',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.consulting_location?.toString(),
      defaultValue: doctor?.consulting_location?.toString(),
      label: 'Consulting Location',
      type: 'string',
      StartAdornmentIcon: <ManWomanIcon className="size-5 text-neutral-500" />,
      mutateFn: mutateDoctorData,
      fieldName: 'consulting_location',
    },
    {
      id: doctor?.postal_barcode?.toString(),
      defaultValue: doctor?.postal_barcode,
      label: 'postal_barcode',
      type: 'string',
      StartAdornmentIcon: <WorldLocationIcon className="size-5 text-neutral-500" />,
      fieldName: 'postal_barcode',
      mutateFn: mutateDoctorData,
    },

    {
      id: doctor?.building?.toString(),
      defaultValue: doctor?.building,
      label: 'Building',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'building',
      mutateFn: mutateDoctorData,
    },

    {
      id: doctor?.street?.toString(),
      defaultValue: doctor?.street,
      label: 'Street',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'street',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.suburb?.toString(),
      defaultValue: doctor?.suburb,
      label: 'Suburb',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'building',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.state?.toString(),
      defaultValue: doctor?.state,
      label: 'State',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'state',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.post_code?.toString(),
      defaultValue: doctor?.post_code,
      label: 'Post Code',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'Post Code',
      mutateFn: mutateDoctorData,
    },
  ];

  const practiceContractDetails = [
    {
      id: doctor?.phone?.toString(),
      defaultValue: doctor?.phone,
      label: 'Phone',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'phone',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.email?.toString(),
      defaultValue: doctor?.email,
      label: 'Email',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      mutateFn: mutateDoctorData,
      fieldName: 'email',
    },
    {
      id: doctor?.mobile?.toString(),
      defaultValue: doctor?.mobile,
      label: 'Mobile',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'mobile',
      mutateFn: mutateDoctorData,
    },

    {
      id: doctor?.ext?.toString(),
      defaultValue: doctor?.ext,
      label: 'Ext',
      type: 'string',
      StartAdornmentIcon: <ManWomanIcon className="size-5 text-neutral-500" />,
      fieldName: 'ext',
      mutateFn: mutateDoctorData,
    },

    {
      id: doctor?.fax?.toString(),
      defaultValue: doctor?.fax,
      label: 'Fax',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'fax',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.secretary_line_1?.toString(),
      defaultValue: doctor?.secretary_line_1,
      label: 'Secretary Line 1',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'secretary_line_1',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.secretary_line_2?.toString(),
      defaultValue: doctor?.secretary_line_2,
      label: 'Secretary Line 2',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'secretary_line_2',
      mutateFn: mutateDoctorData,
    },
  ];

  const others = [
    {
      id: doctor?.job_description?.toString(),
      defaultValue: doctor?.job_description,
      label: 'Job Description',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'job_description',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.salutation?.toString(),
      defaultValue: doctor?.salutation,
      label: 'Salutation',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      mutateFn: mutateDoctorData,
      fieldName: 'salutation',
    },
    {
      id: doctor?.specialty?.toString(),
      defaultValue: doctor?.practice_code,
      label: 'Specialty',
      type: 'string',
      StartAdornmentIcon: <TranslatelanguageIconly className="size-5 text-neutral-500" />,
      fieldName: 'specialty',
      mutateFn: mutateDoctorData,
    },
  ];

  const optionalOthers = [
    {
      id: doctor?.external_reference?.toString(),
      defaultValue: doctor?.external_reference,
      label: 'External Reference',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'external_reference',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.hpi_number?.toString(),
      defaultValue: doctor?.hpi_number,
      label: 'HPI #',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      mutateFn: mutateDoctorData,
      fieldName: 'hpi_number',
    },
    {
      id: doctor?.hpio_number?.toString(),
      defaultValue: doctor?.hpio_number,
      label: 'HPIO #',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'hpio_number',
      mutateFn: mutateDoctorData,
    },
    {
      id: doctor?.edi_address?.toString(),
      defaultValue: doctor?.edi_address,
      label: 'EDI Address',
      type: 'string',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'edi_address',
      mutateFn: mutateDoctorData,
    },
  ];

  return (
    <Modal
      isDismissable
      className="react-aria-Drawer max-w-xl"
      isOpen={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) {
          setSelectedDoctorId(null);
        }
      }}
    >
      <Dialog>
        <div className="mb-0 space-y-6 border-b border-neutral-200 p-0 pb-5">
          <div className="flex w-full items-start justify-between">
            <div className="space-y-3">
              <Avatar className="h-15 w-15">
                <AvatarFallback className="bg-brand-100 text-xl text-white shadow-lg">
                  <AvatarIcon className="h-8.5 w-7" />
                </AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <div className="text-xl leading-tight font-semibold tracking-tight text-neutral-800">
                  {doctor.title && `${doctor.title}. `}
                  {doctor.surname && `${doctor.surname}, `}
                  {doctor.forename && doctor.forename}
                </div>
                <div className="text-neutral-700">{doctor.provider_number}</div>
              </div>
            </div>

            <XIcon
              className="h-4 w-4 cursor-pointer"
              onClick={() => setIsOpen(false)}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-x-8">
          <div className="divide-y divide-neutral-200">
            <CollapsibleElement title="Identification">
              {identification.map((item, index) => (
                <EditableItem
                  key={`general-info-${item.fieldName}-${index}-${Math.random()}`}
                  defaultValue={item.defaultValue}
                  label={item.label}
                  type={item?.type as 'number' | 'date' | 'select' | 'text' | undefined}
                  StartAdornmentIcon={item.StartAdornmentIcon}
                  fieldName={item.fieldName}
                  mutateFn={item.mutateFn}
                  isPatientDataLoading={false}
                  doctorId={doctor.id}
                />
              ))}
            </CollapsibleElement>


            <CollapsibleElement title="Practice Address">
              {practiceAddress.map((item, index) => (
                <EditableItem
                  key={`practice-area-${item.fieldName}-${index}-${Math.random()}`}
                  defaultValue={item.defaultValue}
                  label={item.label}
                  type={item?.type as 'number' | 'date' | 'select' | 'text' | undefined}
                  StartAdornmentIcon={item.StartAdornmentIcon}
                  fieldName={item.fieldName}
                  mutateFn={item.mutateFn}
                  isPatientDataLoading={false}
                  doctorId={doctor.id}
                />
              ))}
            </CollapsibleElement>
          </div>
          <div className="divide-y divide-neutral-200">
            <CollapsibleElement title="Practice Contract Details">
              {practiceContractDetails.map((item, index) => (
                <EditableItem
                  key={`practice-contract-details-${item.fieldName}-${index}-${Math.random()}`}
                  defaultValue={item.defaultValue}
                  label={item.label}
                  type={item?.type as 'number' | 'date' | 'select' | 'text' | undefined}
                  StartAdornmentIcon={item.StartAdornmentIcon}
                  fieldName={item.fieldName}
                  mutateFn={item.mutateFn}
                  isPatientDataLoading={false}
                  doctorId={doctor.id}
                />
              ))}
            </CollapsibleElement>

            <CollapsibleElement title="Others">
              {others.map((item, index) => (
                <EditableItem
                  key={`others-${item.fieldName}-${index}-${Math.random()}`}
                  defaultValue={item.defaultValue}
                  label={item.label}
                  type={item?.type as 'number' | 'date' | 'select' | 'text' | undefined}
                  StartAdornmentIcon={item.StartAdornmentIcon}
                  fieldName={item.fieldName}
                  mutateFn={item.mutateFn}
                  isPatientDataLoading={false}
                  doctorId={doctor.id}
                />
              ))}

              <div className="my-0.5 flex h-8 items-center gap-3 pl-2">
                <span className="text-xs font-semibold text-neutral-800">Referring</span>
                <Switch
                  className="group"
                  isSelected={isReferring}
                  onChange={() => setIsReferring(!isReferring)}
                >
                  <div className="group-data-[selected]:bg-brand-500 relative flex h-4 w-7.5 shrink-0 cursor-default rounded-full bg-neutral-300 p-0.5 transition duration-200 ease-in-out">
                    <span className="absolute top-1/2 left-0.5 h-3 w-3 -translate-y-1/2 translate-x-0 transform rounded-full bg-white transition duration-200 ease-in-out group-data-[selected]:translate-x-3.5" />
                  </div>
                </Switch>
              </div>
              {isReferring &&
                optionalOthers.map((item, index) => (
                  <EditableItem
                    key={`optional-others-${item.fieldName}-${index}-${Math.random()}`}
                    defaultValue={item.defaultValue}
                    label={item.label}
                    type={item?.type as 'number' | 'date' | 'select' | 'text' | undefined}
                    StartAdornmentIcon={item.StartAdornmentIcon}
                    fieldName={item.fieldName}
                    mutateFn={item.mutateFn}
                    isPatientDataLoading={false}
                    doctorId={doctor.id}
                  />
                ))}
            </CollapsibleElement>
          </div>
        </div>

        <div className="flex w-full gap-4">
          <div className="w-[calc(50%-8px)] shrink-0">

          </div>

          <div className="w-[calc(50%-8px)] shrink-0">

          </div>
        </div>
        <div className="flex w-full gap-4">
          <div className="w-[calc(50%-8px)] shrink-0">
          </div>

          <div className="w-[calc(50%-8px)] shrink-0">
          </div>
        </div>
      </Dialog>
    </Modal>
  );
}

function CollapsibleElement({title, children}: {title: string; children: ReactNode}) {
  return (
    <Collapsible defaultOpen={true}>
      <CollapsibleTrigger className="my-5 flex w-full cursor-pointer items-center justify-between text-neutral-700">
        <div className="text-xs font-semibold">{title}</div>
        <ChevronDown className="h-4 w-4 [[data-state=open]_&]:rotate-180" />
      </CollapsibleTrigger>
      <CollapsibleContent className="radix-CollapsibleContent -mx-5 px-3">
        <div className="pb-5">{children}</div>
      </CollapsibleContent>
    </Collapsible>
  );
}
