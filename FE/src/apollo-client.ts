import {ApolloClient, InMemoryCache, createHttpLink, from, split} from '@apollo/client';
import {setContext} from '@apollo/client/link/context';
import {FetchResult} from '@apollo/client/link/core';
import {onError} from '@apollo/client/link/error';
import {GraphQLWsLink} from '@apollo/client/link/subscriptions';
import {Observable, getMainDefinition} from '@apollo/client/utilities';
import {PersistentStorage, persistCache} from 'apollo3-cache-persist';
import axios from 'axios';
import {createClient} from 'graphql-ws';
import {del, get, set} from 'idb-keyval';

import {Paths} from '@/api-types/routePaths.ts';
import authStore from '@/store/auth.store.ts';

import {axiosInstance} from './axios';
import authStore from "@/store/auth.store.ts";

class IdbStorageWrapper<T> implements PersistentStorage<T> {
  getKey(key: string) {
    if (!authStore.tokenSiteId) {
      return key;
    }

    return `${authStore.tokenSiteId}:${key}`;
  }

  getItem(key: string) {
    return get(this.getKey(key));
  }

  removeItem(key: string) {
    return del(this.getKey(key));
  }

  setItem(key: string, value: T) {
    if (value !== null) {
      return set(this.getKey(key), value);
    } else {
      return del(this.getKey(key));
    }
  }
}

const httpLink = createHttpLink({
  uri: window.globalEnv.VITE_APP_HASURA_URL as string,
});

const wsLink = new GraphQLWsLink(
  createClient({
    url: (window.globalEnv.VITE_APP_HASURA_URL as string)?.replace('http', 'ws') as string,
    connectionParams: async () => {
      const accessToken = authStore.accessToken;
      return {
        headers: {
          authorization: `Bearer ${accessToken}`,
        },
      };
    },
  })
);

const splitLink = split(
  ({query}) => {
    const definition = getMainDefinition(query);
    return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
  },
  wsLink,
  httpLink
);

export const inMemoryCache = new InMemoryCache();

await persistCache({
  cache: inMemoryCache,
  storage: new IdbStorageWrapper(),
  serialize: false as any,
});

let validationRequest: Promise<any> | undefined;

const errorLink = onError(({graphQLErrors, operation, forward}) => {
  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      if (err.extensions?.code === 'invalid-jwt') {
        // Token is expired, let axios interceptor handle the refresh
        return new Observable((observer) => {
          if (!validationRequest) {
            validationRequest = axiosInstance.get(Paths.PROFILE).finally(() => {
              validationRequest = undefined;
            });
          }

          // Make a dummy request to trigger axios interceptor
          validationRequest
            .then(() => {
              // After token refresh, retry the failed operation
              const subscriber = {
                next: (result: FetchResult) => observer.next(result),
                error: (error: Error) => observer.error(error),
                complete: () => observer.complete(),
              };
              forward(operation).subscribe(subscriber);
            })
            .catch((error: Error) => {
              observer.error(error);
            });
        });
      }
    }
  }
});

const authLink = setContext(async (_, context) => {
  const token = authStore.accessToken;
  if (!token) {
    authStore.logout();
    window.location.href = '/auth/login';
    return;
  }

  if (!token) {
    const siteId = authStore.tokenSiteId;
    const refreshTokenRequest = axios.post(
      Paths.REFRESH_TOKEN,
      {...(siteId && {site_id: siteId})},
      {
        headers: {
          Authorization: `Bearer ${authStore.refreshToken}`,
        },
      }
    );

    const tokenRes = await refreshTokenRequest;
    authStore.setAccessToken(tokenRes.data.access_token);
  }

  return {
    ...context,
    headers: {
      ...context.headers,
      authorization: token ? `Bearer ${authStore.accessToken}` : {token},
    },
  };
});

export const apolloClient = new ApolloClient({
  link: from([errorLink, authLink, splitLink]),
  cache: inMemoryCache,
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
    },
  },
});
