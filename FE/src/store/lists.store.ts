import {makeAutoObservable} from 'mobx';

import {PropertiesOnly} from '@/@types/utils.ts';
import {apolloClient} from '@/apollo-client.ts';
import {
  getAboriginalList,
  getAddressTypes,
  getLanguageList,
  getNationalities,
  getReportStatuses
} from '@/graphql/lists.ts';
import {getEthnicities, getHealthServicesList, getRFTGenders} from '@/graphql/patients.ts';
import {assignDefinedProps} from '@/store/utils.ts';

export class Gender {
  description!: string;
  gender!: string;
  gender_code!: string;
  id!: number;
  list_option_for_demographics!: boolean;
  list_option_for_preds!: boolean;
  list_option_for_rfts!: boolean;

  constructor(param: Partial<PropertiesOnly<Gender>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }

  static async loadAll() {
    const {data: gendersData} = await apolloClient.query({
      query: getRFTGenders,
    });

    return gendersData?.pred_ref_genders?.map((gender) => {
      return new Gender(gender as any);
    });
  }
}

export class Ethnicity {
  ethnicity!: string;
  description!: string;
  ethnicityid!: number;
  id!: number;
  list_option_for_demographics!: boolean;

  constructor(param: Partial<PropertiesOnly<Ethnicity>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }

  static async loadAll() {
    const {data: ethnicitiesData} = await apolloClient.query({
      query: getEthnicities,
    });

    return ethnicitiesData?.pred_ref_ethnicities?.map((ethnicity) => {
      return new Ethnicity(ethnicity as any);
    });
  }
}

export class AddressType {
  description!: string;
  code!: number;

  constructor(param: Partial<PropertiesOnly<AddressType>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }

  static async loadAll() {
    const {data: addressTypesData} = await apolloClient.query({
      query: getAddressTypes,
    });
    return addressTypesData?.list_addresstype?.map((addressType) => {
      return new AddressType(addressType as any);
    });
  }
}

export class ReportStatus {
  statusid!: string;
  description?: string;
  verified_status?: boolean;
  enabled!: boolean;
  display_order?: number;

  constructor(param: Partial<PropertiesOnly<ReportStatus>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }

  static async loadAll() {
    const {data: addressTypesData} = await apolloClient.query({
      query: getReportStatuses,
    });

    return addressTypesData?.list_reportstatuses?.map((item) => new ReportStatus(item as any));
  }
}

export class Nationality {
  hl7_code!: string;
  hl7_description?: string;
  code?: string;
  description?: string;

  constructor(param: Partial<PropertiesOnly<Nationality>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }

  static async loadAll() {
    const {data} = await apolloClient.query({
      query: getNationalities,
    });

    return data?.list_nationality?.map((item) => new Nationality(item as any));
  }
}

export class Language {
  hl7_code!: string;
  hl7_description?: string;
  code?: string;
  description?: string;
  enabled?: boolean;
  list_language_id?: number;

  constructor(param: Partial<PropertiesOnly<Language>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }

  static async loadAll() {
    const {data} = await apolloClient.query({
      query: getLanguageList,
    });

    return data?.list_language?.map((item) => new Language(item as any));
  }
}

export class AboriginalStatus {
  hl7_code!: string;
  hl7_description?: string;
  code?: string;
  description?: string;
  enabled?: boolean;

  constructor(param: Partial<PropertiesOnly<AboriginalStatus>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }

  static async loadAll() {
    const {data} = await apolloClient.query({
      query: getAboriginalList,
    });

    return data?.list_aboriginalstatus?.map((item) => new AboriginalStatus(item as any));
  }
}

export class HealthService {
  description!: string;
  code!: number;

  constructor(param: Partial<PropertiesOnly<HealthService>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any);
  }
  static async loadAll() {
    const {data} = await apolloClient.query({
      query: getHealthServicesList,
    });

    return data?.list_healthservices?.map((item) => new HealthService(item as any));
  }
}
