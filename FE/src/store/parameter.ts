import {makeAutoObservable} from 'mobx';

import {PropertiesOnly} from '@/@types/utils.ts';
import {apolloClient} from '@/apollo-client.ts';
import {PredMetadata} from '@/features/equations-gli';
import {getParamEquations} from '@/graphql/equations';
import authStore from '@/store/auth.store.ts';
import {Equation} from '@/store/equation.store.ts';
import {globalStore} from '@/store/global.store.ts';
import {assignDefinedProps} from '@/store/utils.ts';

export type ParamEquationMetadata = Required<
  Pick<
    PredMetadata,
    | 'gender_for_rfts_id'
    | 'gender_for_rfts'
    | 'ethnicity'
    | 'age'
    | 'Htcm'
    | 'Wtkg'
    | 'ethnicity_id'
    | 'load_method'
    | 'testdate'
    | 'testid'
  >
> &
  Partial<PredMetadata>;

export enum ParameterUnits {
  SI = 'si',
  TRADITIONAL = 'traditional',
  NOT_SET = 'not_set',
}

export class Parameter {
  parameterid!: number;
  description!: string;
  testid?: number | null;
  longname!: string;
  units!: string;
  units_si!: string;
  units_convert_trad_to_si!: number;
  decimalplaces!: number;
  decimalplaces_si!: number;
  valid_low!: number;
  valid_high!: number;
  valid_low_si!: number;
  valid_high_si!: number;

  private subscription?: ReturnType<ReturnType<typeof apolloClient.watchQuery>['subscribe']>;

  constructor(param: Partial<PropertiesOnly<Parameter>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, param as any, this.setProperty.bind(this) as any);
  }

  setProperty<K extends keyof Parameter>(key: K, value: Parameter[K]) {
    this[key] = value as any;
  }

  static get(name: string) {
    return globalStore.parameters.find((p) => p.description === name);
  }

  static getOrCreate(param: Partial<PropertiesOnly<Parameter>>) {
    let parameter = Parameter.get(param.description!);
    if (!parameter) {
      parameter = new Parameter(param);
      globalStore.parameters.push(parameter);
    }

    return parameter;
  }

  destroy() {
    this.subscription?.unsubscribe();
  }

  get configAwareDecimalPlaces() {
    const defaultUnit = authStore.site?.getConfig('site_parameter_units') ?? ParameterUnits.NOT_SET;
    if (defaultUnit === ParameterUnits.TRADITIONAL) {
      return this.decimalplaces;
    }

    return this.decimalplaces_si;
  }

  get configAwareUnits() {
    const defaultUnit = authStore.site?.getConfig('site_parameter_units') ?? ParameterUnits.NOT_SET;
    if (defaultUnit === ParameterUnits.TRADITIONAL) {
      return this.units;
    }

    return this.units_si;
  }

  async getEquation(predMetadata: ParamEquationMetadata) {
    const equationData = await apolloClient.query({
      query: getParamEquations,
      variables: {
        paramId: this.parameterid,
        genderId: predMetadata.gender_for_rfts_id,
        age: Math.round(predMetadata.age),
        ethnicityId: predMetadata.ethnicity_id,
        method: predMetadata.load_method!,
        testDate: predMetadata.testdate?.toISOString().split('T')[0]!,
        sourceId: predMetadata.sourceid,
        testId: +predMetadata.testid,
        siteId: authStore.site?.id

      },
      fetchPolicy: 'network-only',
    });

    if (!equationData.data.sp_get_eq_for_parameter[0]) {
      return;
    }
    return new Equation(equationData.data.sp_get_eq_for_parameter[0] as any);
  }

  get unitsCorrectionFactor() {
    if (
      authStore.site?.getConfig('site_parameter_units') === ParameterUnits.SI &&
      this.units_convert_trad_to_si
    ) {
      return this.units_convert_trad_to_si;
    }
    return 1;
  }

  async calcPred(predMetadata: ParamEquationMetadata, equation?: Equation, testResult?: number) {
    if (!equation) {
      equation = await this.getEquation(predMetadata);
    }
    const pred = await equation?.calcPred(
      predMetadata,
      testResult ? testResult / this.unitsCorrectionFactor : undefined
    );

    if (pred) {
      pred.mpv *= this.unitsCorrectionFactor;

      if (pred.lln) pred.lln *= this.unitsCorrectionFactor;
      if (pred.uln) pred.uln *= this.unitsCorrectionFactor;
      if (pred.zscore) pred.zscore *= this.unitsCorrectionFactor;
      if (pred.range?.[0]) pred.range[0] *= this.unitsCorrectionFactor;
      if (pred.range?.[1]) pred.range[1] *= this.unitsCorrectionFactor;
    }

    return pred;
  }

  get valueFormater() {
    return new Intl.NumberFormat('en', {
      minimumFractionDigits: this.configAwareDecimalPlaces,
      maximumFractionDigits: this.configAwareDecimalPlaces,
    });
  }
}
