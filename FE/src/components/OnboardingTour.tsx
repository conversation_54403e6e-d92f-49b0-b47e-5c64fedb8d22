import React, {useEffect} from 'react';

import {useQuery} from '@apollo/client';
import {StepType, TourProvider, components, useTour} from '@reactour/tour';
import {X} from 'lucide-react';

import {Paths} from '@/api-types/routePaths.ts';
import {axiosInstance} from '@/axios.ts';
import {GET_SIDEBAR_MODULES} from '@/graphql/sidebar.ts';
import {useApiQuery} from '@/hooks/use-api-query.ts';

import OnboardingStyles from './ui/OnboardingStyles.ts';
import {Button} from './ui/button.tsx';

interface TourStep {
  selector: string;
  content: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: (elem: Element | null) => void;
  actionAfter?: (elem: Element | null) => void;
  disableInteraction?: boolean;
  disableActions?: boolean;
}

const urlToIdMap: Record<string, string> = {
  '/dashboard': 'dashboard',
  '/bookings': 'bookings',
  '/patients': 'patients',
  '/reports': 'reports',
  '/contacts': 'contacts',
  '/data': 'data',
  '/quality-control': 'quality-control',
  '/pdf-import': 'pdf-import',
  '/normal-values': 'normal-values',
};

const moduleDescriptions: Record<string, string> = {
  dashboard: 'View your main dashboard with key metrics and overview of your laboratory operations.',
  bookings: 'Manage patient appointments and booking schedules.',
  patients: 'Access and manage patient records, demographics, and medical history.',
  reports: 'Generate, view, and manage laboratory reports and test results.',
  contacts: 'Manage your contact directory referring doctors.',
  data: 'Access and manage your laboratory data, including test parameters and reference ranges.',
  'quality-control': 'Monitor and manage quality control sessions to ensure accurate test results.',
  'pdf-import': 'Import and process PDF reports using our Magic Import feature.',
  'normal-values': 'Configure and manage normal reference ranges for laboratory tests.',
};

async function markGeneralOnboardingCompleted() {
  try {
    return await axiosInstance.post(Paths.PROFILE, {onboarding: {general: true}});;
  } catch (error) {
    console.error('Error marking general onboarding completed', error);
  }
}

export function CustomNavigation(props: any) {
  return (
    <components.Navigation
      {...props}
      hideDots={true}
    />
  );
}

export function CustomClose() {
  const {setIsOpen} = useTour();

  return (
    <button
      onClick={async () => {
        await markGeneralOnboardingCompleted();
        setIsOpen(false);
      }}
      className="absolute top-6 right-7.5 cursor-pointer"
    >
      <X className="text-brand-700 h-4 w-4" />
    </button>
  );
}

export function CustomBadge(props: any) {
  return <div {...props} />;
}

export const TourStepWrapper = ({children}: {children: React.ReactNode}) => (
  <div className="relative p-4">
    <div className="absolute -top-10 -left-10 flex items-center justify-center">
      <div className="bg-brand2-500 h-40 w-40 rounded-full opacity-35 blur-2xl" />
    </div>
    <div className="relative z-10">{children}</div>
  </div>
);

const TourComponent = () => {
  const {data: sidebarData} = useQuery(GET_SIDEBAR_MODULES);
  const {data: currentUser} = useApiQuery(Paths.PROFILE);

  const hasCompletedTour = currentUser?.onboarding?.general;
  const {setIsOpen, setSteps} = useTour();


  useEffect(() => {
    if (!hasCompletedTour && sidebarData?.sidebar_modules) {
      const timer = setTimeout(() => {
        startTour();
      }, 1000);

      return () => clearTimeout(timer);
    }

    if (hasCompletedTour) {
      setIsOpen(false);
    }
  }, [hasCompletedTour, sidebarData]);

  const startTour = () => {
    const steps: TourStep[] = [
      {
        selector: '#onboarding-root',
        content: (
          <TourStepWrapper>
            <h2 className="mb-3 text-xl font-bold text-gray-800">Welcome to Rezibase! 🎉</h2>
            <p className="mb-4 text-gray-600">
              We're excited to have you on board! Let's take a quick tour of the application to help you get
              started with managing your laboratory operations.
            </p>
            <p className="text-sm text-gray-500">
              This tour will guide you through the main features and modules available in the sidebar.
            </p>

            <button
              className="text-sm text-gray-500 underline"
              onClick={async () => {
                const res= await markGeneralOnboardingCompleted();
                res && setIsOpen(false);
              }}
            >
              Skip Tour
            </button>
          </TourStepWrapper>
        ),
        position: 'center',
        actionAfter: () => {
          const elem = document.querySelector('#nav-user-trigger');
          if (elem) {
            // console.log('x: ', x);
            // console.log('elem', elem.click);
            // elem?.click();
          }
        },
        disableInteraction: true,
        disableActions: true,
      },
      // {
      //   selector: '#nav-user-trigger',
      //   content: (
      //     <TourStepWrapper>
      //       <h2 className="mb-3 text-xl font-bold text-gray-800">
      //         Navigate to settings, sites and much more
      //       </h2>
      //       <p className="mb-4 text-gray-600">
      //         Here you can find all the settings, whether it is about changing sites or viewing admin
      //         settings.
      //       </p>
      //     </TourStepWrapper>
      //   ),
      // },
      // {
      //   selector: '#admin',
      //   content: (
      //     <TourStepWrapper>
      //       <h2 className="mb-3 text-xl font-bold text-gray-800">Admin Settings</h2>
      //       <p className="mb-4 text-gray-600">
      //         Here you can find all the settings, whether it is about changing sites or viewing admin
      //         settings.
      //       </p>
      //     </TourStepWrapper>
      //   ),
      // },
      // {
      //   selector: '#sites',
      //   content: (
      //     <TourStepWrapper>
      //       <h2 className="mb-3 text-xl font-bold text-gray-800">Site Switching</h2>
      //       <p className="mb-4 text-gray-600">
      //         Here you can find all the settings, whether it is about changing sites or viewing admin
      //         settings.
      //       </p>
      //     </TourStepWrapper>
      //   ),
      // },
    ];

    if (sidebarData?.sidebar_modules) {
      sidebarData.sidebar_modules.forEach((module) => {
        const moduleId = urlToIdMap[module.url];
        if (moduleId) {
          steps.push({
            selector: `#${moduleId}`,
            content: (
              <TourStepWrapper>
                <h3 className="mb-2 text-lg font-semibold text-gray-800 capitalize">{module.title}</h3>
                <p className="mb-3 text-gray-600">
                  {moduleDescriptions[moduleId] ||
                    `Access the ${module.title} module to manage related features and data.`}
                </p>
                {module.url === '#' && (
                  <p className="rounded bg-amber-50 p-2 text-sm text-amber-600">
                    <strong>Note:</strong> This module is currently under development.
                  </p>
                )}
              </TourStepWrapper>
            ),
            position: 'right',
          });
        }
      });
    }

    steps.push({
      selector: '#onboarding-root',
      content: (
        <TourStepWrapper>
          <h2 className="mb-3 text-xl font-bold text-gray-800">You're All Set! 🚀</h2>
          <p className="mb-4 text-gray-600">
            Congratulations! You've completed the onboarding tour. You're now ready to start using Rezibase to
            manage your laboratory operations efficiently.
          </p>
          <div className="bg-brand-300/26 rounded-lg p-3">
            <p className="text-brand-700 text-sm">
              <strong className="block">Need Help?</strong> Reach out to the Rezibase support team for
              assistance with any questions or issues.
            </p>

            <p className="text-brand-700 mt-1 text-sm">
              Contact us{' '}
              <a
                className="underline"
                href="https://www.cardiobase.com/contact-us"
                target="_blank"
              >
                here
              </a>{' '}
              or email us at{' '}
              <a
                className="semibold hover:underline"
                href="mailto: <EMAIL>"
              >
                <EMAIL>.
              </a>
            </p>
          </div>
        </TourStepWrapper>
      ),
      position: 'center',
    });

    setSteps && setSteps(steps as StepType[]);
    setIsOpen(true);
  };

  return null;
};

const OnboardingTour: React.FC<{children: React.ReactNode}> = ({children}) => {
  const {setIsOpen} = useTour();

  async function handleTourClose() {
    const res = await markGeneralOnboardingCompleted();
    res && setIsOpen(false);
  }

  return (
    <TourProvider
      steps={[]}
      onClickClose={handleTourClose}
      onClickMask={handleTourClose}
      afterOpen={(target) => {
        if (target) {
          target.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
      }}
      styles={OnboardingStyles}
      className="tour-popover"
      maskClassName="tour-mask"
      highlightedMaskClassName="tour-highlighted"
      disableDotsNavigation={false}
      disableKeyboardNavigation={false}
      disableInteraction={false}
      padding={0}
      components={{
        Navigation: CustomNavigation,
        Badge: CustomBadge,
        Close: CustomClose,
      }}
      badgeContent={({currentStep, totalSteps}) => (
        <div className="text-sm text-neutral-800">
          {currentStep + 1}/{totalSteps}
        </div>
      )}
      showBadge={true}
      nextButton={(props) => (
        <Button
          type="button"
          className="m-0 text-right"
          onPress={async () => {
            if (props?.steps?.length && props.currentStep === props.steps.length - 1) {
              handleTourClose();
            }
            props.setCurrentStep(props.currentStep + 1);
          }}
        >
          {props?.steps?.length && props.currentStep === props.steps.length - 1 ? 'Done' : 'Next'}
        </Button>
      )}
      prevButton={() => <div />}
    >
      <TourComponent />
      {children}
    </TourProvider>
  );
};

export default OnboardingTour;
