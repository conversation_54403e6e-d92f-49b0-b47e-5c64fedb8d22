.GridSheet {
  border: 1px solid var(--border-color-sheet);
  border-radius: 0 0 var(--border-radius) var(--border-radius);

  .GridSheetRow > .GridSheetCell + .GridSheetCell,
  .GridSheetRow > .GridSheetHeader + .GridSheetHeader {
    border-left: 1px solid var(--border-color-cell);
  }

  .GridSheetRow:first-child:not(:only-child) > .GridSheetCell:not(:has(.react-aria-NumberField, .react-aria-TextField, .react-aria-Select)) {
    border-bottom: 1px solid var(--border-color-cell);
  }

  .GridSheetHeader {
    border-bottom: 1px solid var(--border-color-sheet);
  }

  .GridSheetRow:first-child > .GridSheetCell :is(.react-aria-NumberField input, .react-aria-TextField input, .react-aria-Select .react-aria-Button) {
    border-top-color: var(--border-color-sheet);
  }

  .GridSheetRow:not(:first-child) + .GridSheetRow > .GridSheetCell {
    border-top: 1px solid var(--border-color-cell);
  }

  .GridSheetRow > .GridSheetCell:first-child :is(.react-aria-NumberField input, .react-aria-TextField input, .react-aria-Select .react-aria-Button) {
    border-left-color: var(--border-color-sheet);
  }

  .GridSheetRow:last-child > .GridSheetCell :is(.react-aria-NumberField input, .react-aria-TextField input, .react-aria-Select .react-aria-Button) {
    border-bottom-color: var(--border-color-sheet);
  }

  &:has(.GridSheetRow:first-child > .GridSheetCell:first-child .react-aria-Select .react-aria-Button) {
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
  }

  .GridSheetRow:first-child > .GridSheetCell:first-child .react-aria-Select .react-aria-Button {
    border-top-left-radius: var(--border-radius);
  }

  .GridSheetRow:first-child > :is(.GridSheetCell:first-child, .GridSheetHeader:first-child) {
    border-top-left-radius: var(--border-radius);
  }

  .GridSheetRow:first-child > :is(.GridSheetCell:last-child, .GridSheetHeader:last-child) {
    border-top-right-radius: var(--border-radius);
  }

  .GridSheetRow:last-child > :is(.GridSheetCell:first-child, .GridSheetHeader:first-child),
  .GridSheetRow:last-child > .GridSheetCell:first-child .react-aria-NumberField > input {
    border-bottom-left-radius: var(--border-radius);
  }

  .GridSheetRow:last-child > :is(.GridSheetCell:last-child, .GridSheetHeader:last-child),
  .GridSheetRow:last-child > .GridSheetCell:last-child .react-aria-NumberField > input {
    border-bottom-right-radius: var(--border-radius);
  }

  .GridSheetCell {
    padding-inline: calc(var(--spacing) * 1);
    height: var(--row-height);
    font-size: var(--font-size);

    &[data-computed] {
      background-color: var(--non-editable-bg);
    }

    & [readonly] {
      background-color: var(--non-editable-bg);
    }

    &:has(> :is(.react-aria-NumberField, .react-aria-TextField, .react-aria-Select)) {
      padding: 0 !important;
    }

    & > .react-aria-NumberField input,
    & > .react-aria-TextField input,
    & > .react-aria-Select .react-aria-Button {
      border-color: var(--border-color-cell);
    }

    & > :is(.react-aria-NumberField, .react-aria-TextField) input {
      padding-inline: calc(var(--spacing) * 1);
      padding-block: calc(var(--spacing) * 1.5);
    }

    & > .react-aria-Select .react-aria-Button {
      padding-inline: calc(var(--spacing) * 2);
      padding-block: calc(var(--spacing) * 1.5);
    }

    & > .react-aria-NumberField,
    & > .react-aria-TextField,
    & > .react-aria-Select {
      margin: -1px;
      height: calc(100% + 2px);

      & > :is(input, .react-aria-Button)[data-hovered] {
        border-color: var(--color-gray-400) !important;
      }

      & > :is(input, .react-aria-Button):is([data-focus-visible], [data-focused], [data-pressed]) {
        border-color: var(--color-brand2-500) !important;
      }

      input {
        border-radius: 0;
        height: 100%;
        font-size: var(--font-size);
      }

      & > .react-aria-Button {
        border-radius: 0;
        height: 100%;

        svg {
          height: calc(var(--spacing) * 3.5);
          width: calc(var(--spacing) * 3.5);
        }
      }

      & .react-aria-SelectValue {
        font-size: var(--font-size);
      }
    }
  }
}

.flash-bg {
  animation: var(--animate-flash-bg);

  input {
    animation: var(--animate-flash-bg);
  }
}
