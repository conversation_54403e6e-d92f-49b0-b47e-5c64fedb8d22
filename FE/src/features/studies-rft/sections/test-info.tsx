import clsx from 'clsx';
import {
  ComboBox,
  DateInput,
  DateSegment,
  Group,
  Input,
  ListBox,
  ListBoxItem,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
  TimeField,
} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {parseTime} from '@internationalized/date';
import {ChevronDown} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import CircleClockIcon from '@/assets/iconly/CircleClock.svg?react';
import NIL from '@/components/NIL.tsx';
import {Label} from '@/components/ui/Field.tsx';
import {getPreferenceField} from '@/graphql/preferences.ts';

import {RftStore} from '../store/rtf.store.ts';

export const TestInfo = observer(({rftStore}: {rftStore: RftStore}) => {
  const {data: LabsData} = useQuery(getPreferenceField, {
    variables: {fieldName: 'Labs'},
  });
  const {data: lastBdData} = useQuery(getPreferenceField, {
    variables: {fieldName: 'Last_BD'},
  });
  const {data: scientistData} = useQuery(getPreferenceField, {
    variables: {fieldName: 'Scientist'},
  });

  return (
    <div className="space-y-4 px-4 pb-4 text-xs/[1.3]">
      <div className="grid grid-cols-2 items-center gap-x-2">
        <Select
          selectedKey={rftStore.scientist}
          placeholder={!rftStore.isEditing ? ((<NIL className="ml-0" />) as unknown as string) : undefined}
          isDisabled={!rftStore.isEditing}
          onSelectionChange={(val) => rftStore.setProperty('scientist', val as string)}
        >
          <Label className="react-aria-Label mb-1.5 text-[11px]/[1.4] text-neutral-600">Tested By</Label>
          <RACButton className={clsx(rftStore.isEditing && 'hidden')}>
            <SelectValue className={clsx('flex h-8 items-center text-[13px]/[1.3] text-neutral-800')} />
          </RACButton>
          <RACButton className={clsx('react-aria-Button h-8 w-full', !rftStore.isEditing && 'hidden')}>
            <SelectValue className="react-aria-SelectValue text-[13px]/[1.3]" />
            <ChevronDown
              className="size-4 text-gray-400"
              aria-hidden="true"
            />
          </RACButton>
          <Popover className="react-aria-Popover w-auto">
            <ListBox items={scientistData?.prefs_fields?.[0]?.prefs_fielditems ?? []}>
              {(item) => <ListBoxItem id={item.fielditem ?? ''}>{item.fielditem}</ListBoxItem>}
            </ListBox>
          </Popover>
        </Select>

        <TimeField
          value={rftStore.testtime ? parseTime(rftStore.testtime) : null}
          onChange={(val) => rftStore.setProperty('testtime', val?.toString() ?? '')}
          hourCycle={24}
          isDisabled={!rftStore.isEditing}
        >
          <Label className="react-aria-Label mb-1.5 text-[11px]/[1.4] text-neutral-600">Test Time</Label>
          <div
            className={clsx(
              rftStore.isEditing ? 'hidden' : 'flex',
              'h-8 items-center text-[13px]/[1.3] text-neutral-800'
            )}
          >
            <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
          </div>
          <Group
            className={clsx(
              'react-aria-Group data-[focus-within]:border-brand2-400 relative h-8 w-full rounded-md border-gray-300',
              !rftStore.isEditing && 'hidden'
            )}
          >
            <DateInput>
              {(segment) => (
                <DateSegment
                  className="react-aria-DateSegment px-px text-[13px] first:pl-0"
                  segment={segment}
                />
              )}
            </DateInput>
            <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center">
              <CircleClockIcon
                className="h-4 w-4 text-neutral-500"
                color="currentColor"
              />
            </div>
          </Group>
        </TimeField>
      </div>

      <div className="grid grid-cols-2 items-center gap-x-2">
        <Select
          selectedKey={rftStore.lab}
          placeholder={!rftStore.isEditing ? ((<NIL className="ml-0" />) as unknown as string) : undefined}
          onSelectionChange={(val) => rftStore.setProperty('lab', val as string)}
          isDisabled={!rftStore.isEditing}
        >
          <Label className="react-aria-Label mb-1.5 text-[11px]/[1.4] text-neutral-600">Lab</Label>
          <RACButton className={clsx(rftStore.isEditing && 'hidden')}>
            <SelectValue className={clsx('flex h-8 items-center text-[13px]/[1.3] text-neutral-800')} />
          </RACButton>
          <RACButton className={clsx('react-aria-Button h-8 w-full', !rftStore.isEditing && 'hidden')}>
            <SelectValue className="react-aria-SelectValue text-[13px]/[1.3]" />
            <ChevronDown
              className="size-4 text-gray-400"
              aria-hidden="true"
            />
          </RACButton>
          <Popover className="react-aria-Popover w-auto">
            <ListBox items={LabsData?.prefs_fields?.[0]?.prefs_fielditems ?? []}>
              {(item) => <ListBoxItem id={item.fielditem ?? ''}>{item.fielditem}</ListBoxItem>}
            </ListBox>
          </Popover>
        </Select>

        <ComboBox
          allowsCustomValue
          selectedKey={rftStore.bdstatus ?? ''}
          onSelectionChange={(val) => rftStore.setProperty('bdstatus', val as string)}
          isDisabled={!rftStore.isEditing}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              console.log(e.currentTarget.value);
              rftStore.setProperty('bdstatus', e.currentTarget.value);
            }
          }}
        >
          <Label className="react-aria-Label mb-1.5 text-[11px]/[1.4] text-neutral-600">Last BD</Label>
          {!rftStore.isEditing ? (
            rftStore.bdstatus ? (
              <div className="text-xs text-neutral-800">{rftStore.bdstatus}</div>
            ) : (
              <NIL className="ml-0" />
            )
          ) : (
            <div className="relative w-full">
              <Input
                className={clsx(
                  'react-aria-Input placeholder:text-neutral-600 w-full pr-8',
                  rftStore.bdstatus && 'placeholder:text-neutral-800'
                )}
                placeholder={rftStore.bdstatus ?? 'Add Last BD'}
              />
              <RACButton className="react-aria-Button group absolute top-0 right-0 z-1 flex h-full items-center justify-center border-none bg-transparent px-2 shadow-none disabled:hidden">
                <ChevronDown
                  className="size-4 text-gray-400 transition-transform group-data-[open=true]:rotate-180"
                  aria-hidden="true"
                />
              </RACButton>
            </div>
          )}
          <Popover className="react-aria-Popover w-auto">
            <ListBox items={lastBdData?.prefs_fields?.[0]?.prefs_fielditems ?? []}>
              {(item) => <ListBoxItem id={item.fielditem ?? ''}>{item.fielditem}</ListBoxItem>}
            </ListBox>
          </Popover>
        </ComboBox>
      </div>
    </div>
  );
});
